### 彻底重装 Visual Studio Code 指南

当 Visual Studio Code (VS Code) 因插件过多或其他未知原因出现性能问题（例如保存代码时卡顿）时，彻底卸载并重新安装是一个有效的解决方案。本文将详细整理如何完全清除 VS Code 及其相关数据，以确保一个纯净的全新安装。


快捷复制

git clone https://github.com/vber/free-augmentcode.git
cd free-augmentcode
python index.py

C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\unins000.exe
C:\Users\<USER>\.vscode
C:\Users\<USER>\AppData\Roaming\Code

第一步：卸载 VS Code 应用程序

首先，需要将 VS Code 程序本身从系统中移除。

*   使用自带卸载程序：在 VS Code 的安装目录中，可以找到 `"C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\unins000.exe"` ，直接运行该程序即可开始卸载。
*   通过系统设置卸载：在 Windows 系统中，可以通过“控制面板”里的“程序和功能”或“设置”中的“应用”列表，找到 Visual Studio Code 并选择卸载。
*   使用第三方卸载工具：也可以借助如 Geek Uninstaller 等第三方专业卸载工具来移除程序。

第二步：删除插件及用户数据

常规卸载通常不会删除用户的个人配置、插件和缓存文件，这是导致重装后问题依旧的主要原因。 为了实现彻底重装，必须手动删除这些残留数据。

在 Windows 系统中，主要需要清理以下两个位置的文件夹（请将 `用户名` 替换为你的实际用户名）：

1.  插件和扩展文件夹：
    *   路径：`C:\Users\<USER>\.vscode`
    *   说明：此文件夹包含了所有已安装的扩展插件。直接将整个 `.vscode` 文件夹删除。

2.  用户数据和缓存文件夹：
    *   路径：`C:\Users\<USER>\AppData\Roaming\Code`
    *   说明：此文件夹存储了用户的设置、缓存以及其他一些工作区信息。 将整个 `Code` 文件夹删除，可以清除所有个人配置和缓存数据。
    *   提示：`AppData` 文件夹默认是隐藏的，你需要在文件资源管理器的“查看”选项中勾选“隐藏的项目”才能看到它。

第三步：重新安装 VS Code

在确认已完成上述所有清理步骤后，即可开始重新安装。

1.  访问官网：打开 Visual Studio Code 官方网站 (`code.visualstudio.com`)。
2.  下载安装包：网站会自动检测你的操作系统并推荐合适的稳定版（Stable Build）进行下载。
3.  执行安装：下载完成后，运行安装程序，按照指引完成安装。

完成以上步骤后，你将获得一个纯净如初的 Visual Studio Code 环境，可以重新开始配置和使用。