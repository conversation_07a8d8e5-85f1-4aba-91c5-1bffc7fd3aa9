# HTML模板系统使用指南

## 📋 概述

本项目已将原本硬编码在Python代码中的HTML模板提取为独立的模块化系统。这样做的好处包括：

- ✅ **代码分离**: HTML、CSS、JavaScript与Python逻辑分离
- ✅ **易于维护**: 修改页面样式无需改动Python代码
- ✅ **可重用性**: 模板可以在不同场景下重复使用
- ✅ **自定义友好**: 用户可以轻松自定义报告外观

## 🏗️ 系统架构

```
pythia/
├── html_templates.py          # 模板管理器核心文件
├── templates/                 # 模板文件目录
│   ├── main_report.html      # 主报告模板
│   ├── error_page.html       # 错误页面模板
│   ├── styles.css            # CSS样式文件
│   └── chart_script.js       # JavaScript脚本
├── pythia_integrated_complete.py  # 主程序（已更新使用模板）
├── test_templates.py         # 模板系统测试脚本
└── template_usage_example.py # 使用示例脚本
```

## 🚀 快速开始

### 1. 基本使用

```python
from html_templates import HTMLTemplateManager

# 初始化模板管理器
template_manager = HTMLTemplateManager()

# 生成专业报告
report_data = {
    'token_address': 'BQ5jRdxkppHviiqkZSzM7t4CsJaXVjNBuRwVgvn8pump',
    'positive_factors': '<li>强劲买入压力</li><li>高交易活跃度</li>',
    'avg_price_change': 5.25,
    'buy_sell_ratio': 1.8,
    'sentiment': '📈 乐观',
    # ... 更多数据
}

html_content = template_manager.generate_professional_report(report_data)

# 保存到文件
with open('report.html', 'w', encoding='utf-8') as f:
    f.write(html_content)
```

### 2. 生成错误报告

```python
# 生成不同类型的错误报告
network_error = template_manager.generate_error_report("network")
data_error = template_manager.generate_error_report("data")
general_error = template_manager.generate_error_report("general")
```

## 📁 模板文件说明

### main_report.html
主报告模板，包含完整的PYTHIA分析报告结构：
- 页面头部（标题、时间戳、代币信息）
- 三列布局（积极因素、核心指标、交易对数据）
- TradingView图表容器
- 页面底部

**可用变量**:
- `{{title}}` - 页面标题
- `{{token_address}}` - 代币地址
- `{{timestamp}}` - 时间戳
- `{{positive_factors}}` - 积极因素HTML
- `{{avg_price_change}}` - 平均价格变化
- `{{buy_sell_ratio}}` - 买卖比例
- `{{sentiment}}` - 市场情绪
- `{{market_cap}}` - 市值
- `{{volume_24h}}` - 24小时交易量
- `{{liquidity}}` - 流动性
- `{{pairs_count}}` - 交易对数量
- `{{pairs_table}}` - 交易对表格HTML
- `{{liquidity_table}}` - 流动性表格HTML

### error_page.html
错误页面模板，用于显示各种错误信息：

**可用变量**:
- `{{title}}` - 页面标题
- `{{error_icon}}` - 错误图标
- `{{error_title}}` - 错误标题
- `{{error_message}}` - 错误消息
- `{{timestamp}}` - 时间戳
- `{{status}}` - 状态信息
- `{{suggestion}}` - 建议信息

### styles.css
CSS样式文件，定义了完整的页面样式：
- CSS变量系统（颜色、字体、间距）
- 响应式布局
- 深色主题设计
- 专业的表格和卡片样式

### chart_script.js
JavaScript脚本，处理TradingView图表：
- 图表初始化逻辑
- 错误处理机制
- 加载状态管理

## 🎨 自定义指南

### 修改颜色主题

编辑 `templates/styles.css` 中的CSS变量：

```css
:root {
    --bg-primary: #0D1117;      /* 主背景色 */
    --bg-secondary: #101419;    /* 次要背景色 */
    --text-primary: #E6EDF3;    /* 主文字颜色 */
    --accent-primary: #F2BC8C;  /* 强调色 */
    /* 修改这些值来改变主题 */
}
```

### 添加新的数据字段

1. 在模板中添加占位符：
```html
<div class="new-field">{{new_data_field}}</div>
```

2. 在Python代码中提供数据：
```python
report_data = {
    'new_data_field': '新的数据值',
    # ... 其他数据
}
```

### 修改布局结构

编辑 `templates/main_report.html`，调整HTML结构：
- 添加新的模块卡片
- 重新排列列布局
- 修改表格结构

## 🧪 测试和验证

### 运行测试脚本
```bash
python test_templates.py
```

### 运行使用示例
```bash
python template_usage_example.py
```

### 验证生成的HTML
1. 在浏览器中打开生成的HTML文件
2. 检查页面布局和样式
3. 验证数据是否正确显示

## 🔧 API参考

### HTMLTemplateManager类

#### 方法

- `__init__()`: 初始化模板管理器
- `load_template(template_name)`: 加载指定模板
- `load_css()`: 加载CSS样式
- `load_js()`: 加载JavaScript代码
- `render_template(template_name, data)`: 渲染模板
- `generate_professional_report(report_data)`: 生成专业报告
- `generate_error_report(error_type)`: 生成错误报告

#### 错误类型

- `"network"`: 网络连接错误
- `"data"`: 数据获取错误  
- `"general"`: 一般系统错误

## 📝 最佳实践

1. **数据验证**: 在传递给模板前验证数据格式
2. **错误处理**: 使用try-catch包装模板生成代码
3. **性能优化**: 重用HTMLTemplateManager实例
4. **安全考虑**: 对用户输入进行HTML转义
5. **版本控制**: 将templates目录纳入版本控制

## 🐛 故障排除

### 常见问题

1. **模板文件不存在**
   - 确保templates目录存在
   - 检查文件权限

2. **变量未替换**
   - 检查变量名拼写
   - 确保数据字典包含所需键

3. **CSS样式不生效**
   - 验证CSS文件路径
   - 检查CSS语法错误

4. **JavaScript错误**
   - 打开浏览器开发者工具查看错误
   - 检查TradingView脚本加载

## 📞 支持

如果遇到问题或需要帮助：
1. 查看错误日志
2. 运行测试脚本验证系统状态
3. 检查模板文件完整性
4. 参考使用示例代码
