# 🐭 PYTHIA代币全面分析系统

这是一个专门用于分析PYTHIA代币的完整集成系统，包含数据分析、报告生成、Telegram自动发送和定时监控功能。

## ✨ 功能特点

### 📊 数据分析
- 实时价格监控和分析
- 多DEX数据聚合 (Raydium, Orca, Jupiter等)
- 流动性深度分析
- 交易量和买卖压力统计
- 市场情绪智能评估
- 价格一致性检查

### 📈 专业报告
- 自动生成专业级分析报告
- 包含技术指标、风险评估、投资建议
- 支持Markdown格式输出
- 历史数据对比分析

### 📱 Telegram集成
- 自动发送报告到Telegram群组/频道
- 支持长消息自动分割
- 实时状态通知
- 错误处理和重试机制

### ⏰ 定时监控
- 可配置的定时分析任务
- 自动报告生成和发送
- 后台监控模式
- 异常处理和恢复

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置系统
编辑 `config.py` 文件，配置以下信息：
- Telegram Bot Token
- 目标群组/频道ID
- API配置参数
- 分析过滤条件

### 3. 运行系统

#### 使用集成系统（推荐）
```bash
# 交互模式
python run_pythia.py

# 命令行模式
python run_pythia.py quick      # 快速分析
python run_pythia.py report     # 生成完整报告
python run_pythia.py send       # 生成并发送报告到Telegram
python run_pythia.py test       # 测试系统功能
python run_pythia.py monitor    # 启动定时监控 (默认60分钟间隔)
python run_pythia.py monitor 30 # 启动定时监控 (自定义间隔)
```

## 📁 文件结构

```
pythia/
├── pythia_integrated_system.py    # 主系统文件 (完整集成版本)
├── run_pythia.py                  # 快速启动脚本
├── config.py                      # 配置文件
├── requirements.txt               # 依赖包列表
├── data/                          # 报告输出目录
│   └── pythia_analysis_*.md       # 生成的分析报告
├── bot.py                         # Telegram Bot
├── memory.json                    # Bot对话记忆
├── subscriptions.json             # 用户订阅数据
└── README.md                      # 说明文档
```

## ⚙️ 配置说明

### Telegram配置
```python
# Telegram Bot配置
TELEGRAM_BOT_TOKEN = "your_bot_token_here"
TELEGRAM_REPORT_CHATS = [
    "@your_channel",
    "-1001234567890"  # 群组ID
]
```

### 分析配置
```python
# 数据过滤设置
ANALYSIS_CONFIG = {
    "data_filter_settings": {
        "min_market_cap": 50000,      # 最小市值
        "min_liquidity": 10000,       # 最小流动性
        "min_volume_24h": 1000,       # 最小24h交易量
        "min_price": 0.01,            # 最小价格
        "max_price": 1000             # 最大价格
    }
}
```

## 📊 报告内容

生成的分析报告包含：

1. **执行摘要** - 关键指标概览
2. **价格数据** - 主要交易对价格分析
3. **交易活动** - 买卖压力和交易统计
4. **流动性分析** - 流动性分布和健康度
5. **技术指标** - 价格动量和交易量指标
6. **风险评估** - 风险因素识别和预警
7. **投资建议** - 基于数据的投资建议

## 🔧 系统测试

运行系统测试确保所有功能正常：
```bash
python run_pythia.py test
```

测试内容包括：
- API连接测试
- Telegram Bot连接测试
- 消息发送测试
- 数据获取测试

## 📱 Telegram设置

### 1. 创建Bot
1. 联系 @BotFather 创建新Bot
2. 获取Bot Token
3. 将Token配置到 `config.py`

### 2. 添加Bot到群组
1. 将Bot添加到目标群组/频道
2. 给Bot发送消息的权限
3. 获取群组ID (可以是用户名如 @channel 或数字ID)

### 3. 测试发送
```bash
python run_pythia.py test
```

## 🚨 故障排除

### API连接问题
- 检查网络连接
- 确认API端点可访问
- 检查速率限制设置

### Telegram发送失败
- 验证Bot Token正确性
- 确认Bot在目标群组中
- 检查Bot权限设置
- 验证群组ID格式

### 数据分析异常
- 检查过滤条件设置
- 确认搜索关键词配置
- 验证数据源可用性

## 📈 使用建议

1. **首次使用**：先运行测试确保所有功能正常
2. **定时监控**：建议设置30-60分钟的监控间隔
3. **报告频率**：避免过于频繁的报告发送
4. **数据过滤**：根据需要调整过滤条件
5. **备份配置**：定期备份配置文件

## 🔒 免责声明

⚠️ **重要提示**：
- 本工具仅供信息参考，不构成投资建议
- 加密货币投资存在高风险，可能导致本金损失
- 请根据自身风险承受能力谨慎投资
- 建议咨询专业投资顾问

## 📞 支持

如有问题或建议，请通过以下方式联系：
- 官网: https://ratpythia.ai/
- Twitter: @druzbtc, @neirylab
- Telegram: @Pythia_DAO

---

🐭 **"数据驱动决策，理性投资未来"** - Pythia AI

# HTML转图片工具使用说明

## 安装依赖

1. 安装Python依赖包：
```bash
pip install -r requirements.txt
```

2. 安装Playwright浏览器：
```bash
playwright install chromium
```

## 使用方法

### 方法1: 批量转换（推荐）
直接运行脚本，会自动转换data目录下的所有HTML文件：
```bash
python html_to_image.py
```

### 方法2: 转换单个文件
指定HTML文件路径：
```bash
python html_to_image.py "data/pythia_analysis_20250725_063334.html"
```

### 方法3: 使用示例脚本
运行预配置的示例：
```bash
python convert_example.py
```

### 方法4: 在代码中使用
```python
import asyncio
from html_to_image import HTMLToImageConverter

async def convert_my_html():
    converter = HTMLToImageConverter()
    
    # 转换单个文件
    result = await converter.convert_html_to_image(
        html_file_path="data/pythia_analysis_20250725_063334.html",
        width=1920,      # 页面宽度
        quality=95       # 图片质量 (1-100)
    )
    print(f"图片保存在: {result}")

# 运行
asyncio.run(convert_my_html())
```

## 输出说明

- 图片默认保存在 `images/` 目录下
- 文件名格式: `原文件名_时间戳.png`
- 支持PNG和JPEG格式
- 自动截取完整页面

## 参数说明

- `width`: 页面宽度，默认1920px
- `height`: 页面高度，None表示自适应完整页面
- `quality`: 图片质量，1-100，仅对JPEG有效
- `output_path`: 自定义输出路径

## 故障排除

如果遇到问题：

1. 确保已安装Playwright浏览器：
   ```bash
   playwright install chromium
   ```

2. 检查HTML文件路径是否正确

3. 确保有足够的磁盘空间

4. 如果是网络问题，可以尝试离线模式