2025-07-25 07:56:36,415 - INFO - 🚀 PYTHIA自动监控系统启动
2025-07-25 07:56:36,416 - INFO - ==================================================
2025-07-25 07:56:37,078 - INFO - ✅ 消息发送成功到 @RjIOrkEpYxpkOGEy
2025-07-25 07:56:37,079 - INFO - 📂 数据目录中没有HTML文件
2025-07-25 07:56:37,081 - INFO - 👀 开始监控目录: C:\Users\<USER>\Desktop\pythia\data
2025-07-25 07:56:37,081 - INFO - 🔄 等待HTML文件变化...
2025-07-25 07:56:52,278 - INFO - 🆕 检测到新HTML文件: data\pythia_analysis_20250725_075652.html
2025-07-25 07:58:24,480 - INFO - 🚀 PYTHIA自动监控系统启动
2025-07-25 07:58:24,484 - INFO - ==================================================
2025-07-25 07:58:25,135 - INFO - ✅ 消息发送成功到 @RjIOrkEpYxpkOGEy
2025-07-25 07:58:25,136 - INFO - 🔍 启动时发现 1 个HTML文件
2025-07-25 07:58:25,136 - INFO - 📄 处理最新文件: pythia_analysis_20250725_075652.html
2025-07-25 07:58:25,138 - INFO - 🔄 开始处理HTML文件: pythia_analysis_20250725_075652.html
2025-07-25 07:58:28,507 - ERROR - ❌ 处理HTML文件时出错: BrowserType.launch: Target page, context or browser has been closed
Browser logs:

<launching> C:\Users\<USER>\AppData\Local\ms-playwright\chromium_headless_shell-1179\chrome-win\headless_shell.exe --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --enable-automation --headless --hide-scrollbars --mute-audio --blink-settings=primaryHoverType=2,availableHoverTypes=2,primaryPointerType=4,availablePointerTypes=4 --no-sandbox --no-sandbox --disable-dev-shm-usage --disable-gpu --disable-web-security --disable-features=VizDisplayCompositor --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-renderer-backgrounding --user-data-dir=C:\Users\<USER>\AppData\Local\Temp\playwright_chromiumdev_profile-e6YsMO --remote-debugging-pipe --no-startup-window
<launched> pid=116648
Call log:
  - <launching> C:\Users\<USER>\AppData\Local\ms-playwright\chromium_headless_shell-1179\chrome-win\headless_shell.exe --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --enable-automation --headless --hide-scrollbars --mute-audio --blink-settings=primaryHoverType=2,availableHoverTypes=2,primaryPointerType=4,availablePointerTypes=4 --no-sandbox --no-sandbox --disable-dev-shm-usage --disable-gpu --disable-web-security --disable-features=VizDisplayCompositor --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-renderer-backgrounding --user-data-dir=C:\Users\<USER>\AppData\Local\Temp\playwright_chromiumdev_profile-e6YsMO --remote-debugging-pipe --no-startup-window
  - <launched> pid=116648

2025-07-25 07:58:28,511 - INFO - 👀 开始监控目录: C:\Users\<USER>\Desktop\pythia\data
2025-07-25 07:58:28,511 - INFO - 🔄 等待HTML文件变化...
