<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=1280, initial-scale=1.0">
    <title>PYTHIA 分析报告 (最终复刻版)</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&family=JetBrains+Mono:wght@400;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --bg-primary: #0D1117;
            --bg-secondary: #101419;
            --bg-module: #161B22;
            --text-primary: #E6EDF3;
            --text-secondary: #B0BAC6;
            --text-muted: #7D8590;
            --accent-primary: #F2BC8C;
            --border-primary: #30363D;
            --border-secondary: #21262D;
            --success: #28A745;
            --danger: #F59E0B;
            
            /* 字体大小变量 - 弹性计算 */
            --base-font-size: 1rem;
            --small-font-size: 0.85rem;
            --medium-font-size: 1.1rem;
            --large-font-size: 1.3rem;
            --xlarge-font-size: 1.6rem;
            --xxlarge-font-size: 2rem;
            
            /* 间距变量 */
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 0.75rem;
            --spacing-lg: 1rem;
            --spacing-xl: 1.5rem;
        }
        *, *::before, *::after {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        body, html {
            font-family: 'Noto Sans SC', sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: var(--base-font-size);
            line-height: 1.4;
            -webkit-font-smoothing: antialiased;
            overflow: hidden;
            margin: 0;
            padding: 0;
            width: 100vw;
            height: 100vh;
        }
        .report-container {
            width: 1280px;
            height: 720px;
            background: var(--bg-primary);
            display: flex;
            flex-direction: column;
            margin: 0 auto;
            position: relative;
        }
        .report-header {
            padding: 8px 20px;
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--border-primary);
            flex-shrink: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 50px;
        }
        .header-title h1 {
            font-family: 'Noto Sans SC', sans-serif;
            font-size: var(--xlarge-font-size);
            font-weight: 700;
        }
        .header-title h1 .logo {
            font-family: 'JetBrains Mono', monospace;
            margin-right: 6px;
            color: var(--accent-primary);
        }
        .header-meta {
            background-color: #0D1117;
            border-radius: 12px;
            padding: var(--spacing-xs) var(--spacing-md);
            font-size: var(--small-font-size);
            color: var(--text-muted);
            font-family: 'JetBrains Mono', monospace;
        }
        .header-meta strong {
            color: var(--text-secondary);
        }
        .report-main {
            flex-grow: 1;
            padding: 10px;
            display: flex;
            gap: 10px;
            overflow: hidden;
            height: calc(720px - 50px - 25px);
        }
        .left-column {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
            height: 100%;
        }
        .center-column {
            flex: 2;
            display: flex;
            flex-direction: column;
            gap: 8px;
            height: 100%;
        }
        .right-column {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
            height: 100%;
        }
        .column {
            display: flex;
            flex-direction: column;
            gap: 12px;
            min-height: 0;
            overflow: hidden;
        }
        .module-card {
            background: var(--bg-module);
            border-radius: 6px;
            border: 1px solid var(--border-primary);
            padding: 12px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        .module-card .module-title {
            font-size: var(--medium-font-size);
            font-weight: 500;
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            flex-shrink: 0;
        }
        .module-card.stretchy {
            flex-grow: 1;
            min-height: 0;
        }
        .analysis-list {
            list-style: none;
            font-size: var(--base-font-size);
            padding-left: var(--spacing-xs);
            overflow: hidden;
            flex-grow: 1;
        }
        .analysis-list li {
            margin-bottom: var(--spacing-sm);
            line-height: 1.3;
        }
        .enhanced-metrics-table {
            width: 100%;
            border-collapse: collapse;
            flex-grow: 1;
        }
        .enhanced-metrics-table td {
            padding: var(--spacing-sm) var(--spacing-xs);
            border-bottom: 1px solid var(--border-secondary);
            font-size: var(--base-font-size);
            vertical-align: middle;
        }
        .enhanced-metrics-table tr:last-child td {
            border-bottom: none;
            padding-bottom: 0;
        }
        .enhanced-metrics-table tr:first-child td {
            padding-top: 0;
        }
        .enhanced-metrics-table .metric-label {
            color: var(--text-muted);
            line-height: 1.2;
        }
        .enhanced-metrics-table .metric-value {
            font-weight: 500;
            text-align: right;
            font-family: 'JetBrains Mono', monospace;
            color: var(--text-primary);
        }
        .core-metrics-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 0;
        }
        .metric-item {
            background-color: var(--bg-secondary);
            border-radius: 4px;
            padding: 8px;
        }
        .metric-item .label {
            font-size: var(--small-font-size);
            margin-bottom: var(--spacing-xs);
            color: var(--text-muted);
        }
        .metric-item .value {
            font-size: var(--large-font-size);
            font-weight: 700;
            font-family: 'JetBrains Mono', monospace;
        }
        .chart-module {
            flex-grow: 1;
            padding: 12px;
            min-height: 0;
        }
        .chart-container {
            flex-grow: 1;
            min-height: 0;
            height: 100%;
        }
        #chart_container {
            width: 100%;
            height: 100%;
        }
        .table-module {
            flex-grow: 1;
            overflow: hidden;
            min-height: 0;
        }
        .table-container {
            height: 100%;
            overflow: hidden;
            flex-grow: 1;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: var(--base-font-size);
        }
        th, td {
            padding: var(--spacing-sm) var(--spacing-xs);
            text-align: left;
            border-bottom: 1px solid var(--border-secondary);
        }
        th {
            font-size: var(--small-font-size);
            text-transform: uppercase;
            color: var(--text-muted);
            position: sticky;
            top: 0;
            background: var(--bg-module);
            padding-bottom: var(--spacing-sm);
        }
        
        /* 交易对详情表格 - 固定字体大小，完美填充 */
        .table-module table {
            font-size: 11px;
            height: 100%;
            width: 100%;
        }
        .table-module th {
            font-size: 10px;
            padding: 4px 3px;
            padding-bottom: 6px;
            line-height: 1.1;
            font-weight: 600;
        }
        .table-module td {
            padding: 4px 3px;
            font-size: 11px;
            line-height: 1.2;
            vertical-align: top;
        }
        .table-module small {
            font-size: 9px;
            line-height: 1.1;
            opacity: 0.8;
        }
        
        /* 流动性分布表格 - 与交易对详情表格统一字体大小 */
        .liquidity-table table {
            font-size: 11px !important;
            height: 100% !important;
        }
        .liquidity-table th {
            font-size: 10px !important;
            padding: 4px 3px !important;
            padding-bottom: 6px !important;
            line-height: 1.1 !important;
            font-weight: 600 !important;
        }
        .liquidity-table td {
            padding: 4px 3px !important;
            font-size: 11px !important;
            line-height: 1.2 !important;
            vertical-align: top !important;
        }
        .liquidity-table small {
            font-size: 9px !important;
            line-height: 1.1 !important;
            opacity: 0.8 !important;
        }
        
        /* 确保流动性表格充分利用垂直空间 - 强制填充 */
        .liquidity-table .table-container {
            height: 100% !important;
            display: flex !important;
            flex-direction: column !important;
        }
        .liquidity-table table {
            height: 100% !important;
            flex-grow: 1 !important;
            display: table !important;
        }
        .liquidity-table tbody {
            height: 100% !important;
            display: table-row-group !important;
        }
        .liquidity-table tbody tr {
            height: 20% !important;
            display: table-row !important;
        }
        .liquidity-table thead {
            display: table-header-group !important;
        }
        .liquidity-table thead tr {
            height: 20% !important;
        }
        
        /* 确保表格容器无滚动条，完美填充 */
        .table-container {
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        .table-container table {
            flex-grow: 1;
            height: 100%;
            table-layout: fixed;
        }
        
        /* 表格列宽优化 */
        .table-module th:nth-child(1),
        .table-module td:nth-child(1) {
            width: 35%;
        }
        .table-module th:nth-child(2),
        .table-module td:nth-child(2) {
            width: 25%;
        }
        .table-module th:nth-child(3),
        .table-module td:nth-child(3) {
            width: 20%;
        }
        .table-module th:nth-child(4),
        .table-module td:nth-child(4) {
            width: 20%;
        }
        tr:last-child td {
            border-bottom: none;
        }
        .dex-distribution {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            overflow: hidden;
            height: 100%;
        }
        .dex-tag {
            background-color: var(--bg-secondary);
            color: var(--text-secondary);
            font-size: var(--small-font-size);
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: 3px;
            white-space: nowrap;
        }
        #liquidity-container {
            font-size: var(--base-font-size);
            overflow-y: auto;
            max-height: 100px;
        }
        .chart-tabs {
            display: flex;
            margin-bottom: 10px;
            border-bottom: 1px solid var(--border-primary);
        }
        .tab-btn {
            background: none;
            border: none;
            color: var(--text-muted);
            padding: var(--spacing-sm) var(--spacing-md);
            font-size: var(--base-font-size);
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
        }
        .tab-btn:hover {
            color: var(--text-secondary);
        }
        .tab-btn.active {
            color: var(--accent-primary);
            border-bottom-color: var(--accent-primary);
        }
        .chart-content {
            display: none;
            height: 100%;
            width: 100%;
        }
        .chart-content.active {
            display: block;
        }
        #tradingview_chart {
            width: 100%;
            height: 100%;
        }
        canvas {
            width: 100% !important;
            height: 100% !important;
        }
        .footer {
            padding: var(--spacing-sm);
            border-top: 1px solid var(--border-primary);
            font-size: var(--small-font-size);
            color: var(--text-muted);
            text-align: center;
            flex-shrink: 0;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <div class="report-container">
        <header class="report-header">
            <div class="header-title">
                <h1><span class="logo">🐭 PYTHIA</span>分析报告</h1>
            </div>
            <div class="header-meta">
                <span><strong>Token:</strong> CreiuhfwdWCN...</span> |
                <span><strong>Chain:</strong> Solana</span> |
                <span><strong>Time:</strong> 2025-07-25 10:26</span>
            </div>
        </header>
        <main class="report-main">
            <!-- LEFT COLUMN -->
            <div class="left-column">
                <div class="module-card">
                    <h2 class="module-title">✅ 积极因素</h2>
                    <ul class="analysis-list"><li>高交易活跃度: 日交易量>$6M</li><li>多DEX支持: 分布在4个DEX</li></ul>
                </div>
                <div class="module-card stretchy">
                    <h2 class="module-title">🔬 增强指标</h2>
                    <table class="enhanced-metrics-table">
                        <tbody>
                            <tr>
                                <td class="metric-label">平均价格变化</td>
                                <td class="metric-value">-2.00%</td>
                            </tr>
                            <tr>
                                <td class="metric-label">价格一致性</td>
                                <td class="metric-value">2.88%</td>
                            </tr>
                            <tr>
                                <td class="metric-label">交易活跃度</td>
                                <td class="metric-value">2459 笔/对</td>
                            </tr>
                            <tr>
                                <td class="metric-label">市场深度</td>
                                <td class="metric-value">0.138</td>
                            </tr>
                            <tr>
                                <td class="metric-label">情绪指数</td>
                                <td class="metric-value">📉 悲观</td>
                            </tr>
                            <tr>
                                <td class="metric-label">买卖比</td>
                                <td class="metric-value">1.04:1</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- CENTER COLUMN -->
            <div class="center-column">
                <div class="module-card">
                    <div class="core-metrics-grid">
                        <div class="metric-item">
                            <div class="label">市值</div>
                            <div class="value">$94.85M</div>
                        </div>
                        <div class="metric-item">
                            <div class="label">24h 交易量</div>
                            <div class="value">$5.94M</div>
                        </div>
                        <div class="metric-item">
                            <div class="label">流动性</div>
                            <div class="value">$13.12M</div>
                        </div>
                        <div class="metric-item">
                            <div class="label">换手率</div>
                            <div class="value">6.3%</div>
                        </div>
                    </div>
                </div>
                <div class="module-card stretchy chart-module">
                    <h2 class="module-title">📈 价格走势图 (24H)</h2>
                    <div class="chart-container">
                        <div id="chart_container"></div>
                    </div>
                </div>
            </div>
            <!-- RIGHT COLUMN -->
            <div class="right-column">
                <div class="module-card table-module" style="flex: 1; min-height: 0;">
                    <h2 class="module-title">💰 交易对详情</h2>
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>交易对 / DEX</th>
                                    <th>价格</th>
                                    <th>24h%</th>
                                    <th>交易量</th>
                                </tr>
                            </thead>
                            <tbody>
                <tr>
                    <td>PYTHIA/SOL<br><small style="color: var(--text-muted);">meteora</small></td>
                    <td>$0.094850</td>
                    <td style="color: #DC3545;">-5.13%</td>
                    <td>$5.73M</td>
                </tr>
                <tr>
                    <td>PYTHIA/SOL<br><small style="color: var(--text-muted);">raydium</small></td>
                    <td>$0.094980</td>
                    <td style="color: #DC3545;">-5.07%</td>
                    <td>$0.20M</td>
                </tr>
                <tr>
                    <td>Pythia/WETH<br><small style="color: var(--text-muted);">uniswap</small></td>
                    <td>$0.094480</td>
                    <td style="color: #DC3545;">-5.56%</td>
                    <td>$0.01M</td>
                </tr>
                <tr>
                    <td>LEGIT/PYTHIA<br><small style="color: var(--text-muted);">orca</small></td>
                    <td>$0.000554</td>
                    <td style="color: #28A745;">+1.75%</td>
                    <td>$0.00M</td>
                </tr>
                <tr>
                    <td>PYTHIA/SOL<br><small style="color: var(--text-muted);">meteora</small></td>
                    <td>$0.097200</td>
                    <td style="color: #DC3545;">+0.00%</td>
                    <td>$0.00M</td>
                </tr>
                <tr>
                    <td>PYTHIA/SOL<br><small style="color: var(--text-muted);">raydium</small></td>
                    <td>$0.095960</td>
                    <td style="color: #DC3545;">+0.00%</td>
                    <td>$0.00M</td>
                </tr>
                <tr>
                    <td>Pythia/SOL<br><small style="color: var(--text-muted);">raydium</small></td>
                    <td>$0.000030</td>
                    <td style="color: #DC3545;">+0.00%</td>
                    <td>$0.00M</td>
                </tr></tbody>
                        </table>
                    </div>
                </div>
                <div class="module-card table-module liquidity-table" style="flex: 1; min-height: 0;">
                    <h2 class="module-title">💧 流动性分布</h2>
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>交易对 / DEX</th>
                                    <th>流动性</th>
                                    <th>占比</th>
                                </tr>
                            </thead>
                            <tbody>
                    <tr>
                        <td>PYTHIA/SOL<br><small style="color: var(--text-muted);">meteora</small></td>
                        <td>$9.94M</td>
                        <td>75.7%</td>
                    </tr>
                    <tr>
                        <td>PYTHIA/SOL<br><small style="color: var(--text-muted);">raydium</small></td>
                        <td>$3.05M</td>
                        <td>23.2%</td>
                    </tr>
                    <tr>
                        <td>PYTHIA/SOL<br><small style="color: var(--text-muted);">meteora</small></td>
                        <td>$0.07M</td>
                        <td>0.5%</td>
                    </tr>
                    <tr>
                        <td>Pythia/SOL<br><small style="color: var(--text-muted);">raydium</small></td>
                        <td>$0.04M</td>
                        <td>0.3%</td>
                    </tr>
                    <tr>
                        <td>Pythia/WETH<br><small style="color: var(--text-muted);">uniswap</small></td>
                        <td>$0.03M</td>
                        <td>0.2%</td>
                    </tr>
                    <tr>
                        <td>PYTHIA/SOL<br><small style="color: var(--text-muted);">raydium</small></td>
                        <td>$0.00M</td>
                        <td>0.0%</td>
                    </tr>
                    <tr>
                        <td>LEGIT/PYTHIA<br><small style="color: var(--text-muted);">orca</small></td>
                        <td>$0.00M</td>
                        <td>0.0%</td>
                    </tr></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
        <footer class="footer">
            PYTHIA AI 增强分析工具 v3.0 | "数据驱动决策，理性投资未来"
        </footer>
    </div>
    <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
    <script type="text/javascript">
        window.addEventListener('load', function () {
            new TradingView.widget({
                "autosize": true,
                "symbol": "CRYPTO:PYTHIAUSD",
                "interval": "30",
                "theme": "dark",
                "style": "1",
                "locale": "zh_CN",
                "toolbar_bg": "#161B22",
                "enable_publishing": false,
                "hide_top_toolbar": true,
                "hide_side_toolbar": true,
                "hide_legend": true,
                "container_id": "chart_container",
                "overrides": {
                    "paneProperties.background": "#161B22",
                    "paneProperties.vertGridProperties.color": "rgba(255,255,255,0.05)",
                    "paneProperties.horzGridProperties.color": "rgba(255,255,255,0.05)",
                    "mainSeriesProperties.candleStyle.upColor": "#28A745",
                    "mainSeriesProperties.candleStyle.downColor": "#DC3545",
                    "mainSeriesProperties.candleStyle.borderUpColor": "#28A745",
                    "mainSeriesProperties.candleStyle.borderDownColor": "#DC3545",
                    "scalesProperties.textColor": "#7D8590"
                }
            });
        });
    </script>
</body>
</html>