<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=1280, initial-scale=1.0">
    <title>PYTHIA 分析报告</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: #0D1117;
            color: #E6EDF3;
            margin: 0;
            padding: 20px;
            width: 1280px;
            height: 720px;
            overflow: hidden;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr 2fr 1fr;
            gap: 20px;
            height: 100%;
        }
        .module {
            background: #161B22;
            border: 1px solid #30363D;
            border-radius: 8px;
            padding: 15px;
            overflow: hidden;
        }
        .module h2 {
            font-size: 1.2rem;
            margin-bottom: 10px;
            color: #F2BC8C;
        }
        .metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }
        .metric {
            background: #0D1117;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        .metric .label {
            font-size: 0.8rem;
            color: #7D8590;
            margin-bottom: 5px;
        }
        .metric .value {
            font-size: 1.2rem;
            font-weight: bold;
            color: #E6EDF3;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.9rem;
        }
        th, td {
            padding: 8px 4px;
            text-align: left;
            border-bottom: 1px solid #30363D;
        }
        th {
            color: #7D8590;
            font-size: 0.8rem;
        }
        .chart-container {
            height: 300px;
            background: #0D1117;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #7D8590;
        }
        #chart_container {
            width: 100%;
            height: 100%;
        }
        ul {
            list-style: none;
            padding: 0;
        }
        li {
            margin-bottom: 8px;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="left-column">
            <div class="module">
                <h2>✅ 积极因素</h2>
                <ul><li>高交易活跃度: 日交易量$7M</li><li>多DEX支持: 分布在4个DEX</li></ul>
            </div>
            <div class="module">
                <h2>🔬 关键指标</h2>
                <table>
                    <tr><td>平均价格变化</td><td>-1.21%</td></tr>
                    <tr><td>买卖比例</td><td>1.01:1</td></tr>
                    <tr><td>情绪指数</td><td>📉 悲观</td></tr>
                    <tr><td>换手率</td><td>7.1%</td></tr>
                </table>
            </div>
        </div>
        <div class="center-column">
            <div class="module">
                <div class="metrics">
                    <div class="metric">
                        <div class="label">市值</div>
                        <div class="value">$94.1M</div>
                    </div>
                    <div class="metric">
                        <div class="label">24h交易量</div>
                        <div class="value">$6.7M</div>
                    </div>
                    <div class="metric">
                        <div class="label">流动性</div>
                        <div class="value">$13.0M</div>
                    </div>
                    <div class="metric">
                        <div class="label">交易对数</div>
                        <div class="value">7</div>
                    </div>
                </div>
            </div>
            <div class="module" style="flex-grow: 1;">
                <h2>📈 价格走势图</h2>
                <div class="chart-container">
                    <div id="chart_container"></div>
                </div>
            </div>
        </div>
        <div class="right-column">
            <div class="module">
                <h2>💰 主要交易对</h2>
                <table>
                    <thead>
                        <tr><th>交易对</th><th>价格</th><th>24h%</th><th>交易量</th></tr>
                    </thead>
                    <tbody>
                <tr>
                    <td>PYTHIA/SOL<br><small style="color: #7D8590;">meteora</small></td>
                    <td>$0.094070</td>
                    <td style="color: #DC3545;">-3.98%</td>
                    <td>$6.59M</td>
                </tr>
                <tr>
                    <td>PYTHIA/SOL<br><small style="color: #7D8590;">raydium</small></td>
                    <td>$0.093930</td>
                    <td style="color: #DC3545;">-3.60%</td>
                    <td>$0.13M</td>
                </tr>
                <tr>
                    <td>Pythia/WETH<br><small style="color: #7D8590;">uniswap</small></td>
                    <td>$0.093730</td>
                    <td style="color: #DC3545;">-3.47%</td>
                    <td>$0.01M</td>
                </tr>
                <tr>
                    <td>LEGIT/PYTHIA<br><small style="color: #7D8590;">orca</small></td>
                    <td>$0.000527</td>
                    <td style="color: #28A745;">+0.48%</td>
                    <td>$0.00M</td>
                </tr>
                <tr>
                    <td>PYTHIA/SOL<br><small style="color: #7D8590;">meteora</small></td>
                    <td>$0.090960</td>
                    <td style="color: #DC3545;">+0.00%</td>
                    <td>$0.00M</td>
                </tr>
                <tr>
                    <td>Pythia/SOL<br><small style="color: #7D8590;">raydium</small></td>
                    <td>$0.000030</td>
                    <td style="color: #DC3545;">+0.00%</td>
                    <td>$0.00M</td>
                </tr>
                <tr>
                    <td>PYTHIA/SOL<br><small style="color: #7D8590;">orca</small></td>
                    <td>$0.093610</td>
                    <td style="color: #28A745;">+2.12%</td>
                    <td>$0.00M</td>
                </tr></tbody>
                </table>
            </div>
            <div class="module">
                <h2>💧 流动性分布</h2>
                <table>
                    <thead>
                        <tr><th>交易对</th><th>流动性</th><th>占比</th></tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td>PYTHIA/SOL<br><small style="color: #7D8590;">meteora</small></td>
                        <td>$9.86M</td>
                        <td>75.8%</td>
                    </tr>
                    <tr>
                        <td>PYTHIA/SOL<br><small style="color: #7D8590;">raydium</small></td>
                        <td>$3.01M</td>
                        <td>23.2%</td>
                    </tr>
                    <tr>
                        <td>PYTHIA/SOL<br><small style="color: #7D8590;">meteora</small></td>
                        <td>$0.06M</td>
                        <td>0.5%</td>
                    </tr>
                    <tr>
                        <td>Pythia/SOL<br><small style="color: #7D8590;">raydium</small></td>
                        <td>$0.04M</td>
                        <td>0.3%</td>
                    </tr>
                    <tr>
                        <td>Pythia/WETH<br><small style="color: #7D8590;">uniswap</small></td>
                        <td>$0.03M</td>
                        <td>0.2%</td>
                    </tr></tbody>
                </table>
            </div>
        </div>
    </div>
    <!-- TradingView Widget Script -->
    <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
    <script type="text/javascript">
        // 确保TradingView全局变量可用，兼容html_to_image_headless.py
        window.TradingView = window.TradingView || {};

        // 图表加载状态标记
        window.chartLoaded = false;
        window.tradingViewLoaded = false;

        function initializeTradingViewChart() {
            try {
                // 检查容器是否存在
                const container = document.getElementById('chart_container');
                if (!container) {
                    console.error('Chart container not found');
                    return;
                }

                // 标记TradingView已加载
                window.tradingViewLoaded = true;

                // 创建TradingView图表
                const widget = new TradingView.widget({
                    "autosize": true,
                    "symbol": "CRYPTO:PYTHIAUSD",
                    "interval": "30",
                    "theme": "dark",
                    "style": "1",
                    "locale": "zh_CN",
                    "toolbar_bg": "#161B22",
                    "enable_publishing": false,
                    "hide_top_toolbar": true,
                    "hide_side_toolbar": true,
                    "hide_legend": true,
                    "container_id": "chart_container",
                    "overrides": {
                        "paneProperties.background": "#0D1117",
                        "paneProperties.vertGridProperties.color": "rgba(255,255,255,0.05)",
                        "paneProperties.horzGridProperties.color": "rgba(255,255,255,0.05)",
                        "mainSeriesProperties.candleStyle.upColor": "#28A745",
                        "mainSeriesProperties.candleStyle.downColor": "#DC3545",
                        "scalesProperties.textColor": "#7D8590"
                    },
                    "onChartReady": function() {
                        // 图表准备就绪
                        window.chartLoaded = true;
                        console.log('TradingView chart loaded successfully');
                    }
                });

                // 备用标记，防止onChartReady不触发
                setTimeout(function() {
                    window.chartLoaded = true;
                }, 5000);

            } catch (error) {
                console.error('TradingView chart initialization error:', error);
                // 即使出错也标记为已加载，避免无限等待
                window.chartLoaded = true;
                window.tradingViewLoaded = true;
            }
        }

        // 页面加载完成后初始化图表
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeTradingViewChart);
        } else {
            initializeTradingViewChart();
        }

        // 窗口加载完成后的备用初始化
        window.addEventListener('load', function() {
            setTimeout(function() {
                if (!window.tradingViewLoaded) {
                    initializeTradingViewChart();
                }
                // 最终备用标记
                setTimeout(function() {
                    window.chartLoaded = true;
                    window.tradingViewLoaded = true;
                }, 3000);
            }, 1000);
        });
    </script>
</body>
</html>