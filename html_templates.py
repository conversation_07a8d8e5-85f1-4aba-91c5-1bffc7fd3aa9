#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML模板管理器
提供可重用的HTML模板，支持动态数据填充
"""

from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path
import os


class HTMLTemplateManager:
    """HTML模板管理器 - 管理所有HTML模板"""
    
    def __init__(self):
        self.templates_dir = Path("templates")
        self.templates_dir.mkdir(exist_ok=True)
        
        # 初始化模板
        self._initialize_templates()
    
    def _initialize_templates(self):
        """初始化所有模板文件"""
        # 创建主报告模板
        self._create_main_report_template()
        # 创建错误页面模板
        self._create_error_template()
        # 创建CSS样式模板
        self._create_css_template()
        # 创建JavaScript模板
        self._create_js_template()
    
    def _create_main_report_template(self):
        """创建主报告HTML模板"""
        template_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=1280, initial-scale=1.0">
    <title>{{title}}</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&family=JetBrains+Mono:wght@400;700&display=swap" rel="stylesheet">
    <style>
        {{css_content}}
    </style>
</head>
<body>
    <div class="report-container">
        <header class="report-header">
            <div class="header-title">
                <h1><span class="logo">🐭 PYTHIA</span>分析报告</h1>
            </div>
            <div class="header-meta">
                <span><strong>Token:</strong> {{token_address}}</span> |
                <span><strong>Chain:</strong> {{chain}}</span> |
                <span><strong>Time:</strong> {{timestamp}}</span>
            </div>
        </header>
        <main class="report-main">
            <!-- LEFT COLUMN -->
            <div class="left-column">
                <div class="module-card">
                    <h2 class="module-title">✅ 积极因素</h2>
                    <ul class="analysis-list">{{positive_factors}}</ul>
                </div>
                <div class="module-card stretchy">
                    <h2 class="module-title">🔬 增强指标</h2>
                    <table class="enhanced-metrics-table">
                        <tbody>
                            <tr>
                                <td class="metric-label">平均价格变化</td>
                                <td class="metric-value">{{avg_price_change}}%</td>
                            </tr>
                            <tr>
                                <td class="metric-label">买卖比例</td>
                                <td class="metric-value">{{buy_sell_ratio}}:1</td>
                            </tr>
                            <tr>
                                <td class="metric-label">情绪指数</td>
                                <td class="metric-value">{{sentiment}}</td>
                            </tr>
                            <tr>
                                <td class="metric-label">换手率</td>
                                <td class="metric-value">{{volume_to_mcap}}%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- CENTER COLUMN -->
            <div class="center-column">
                <div class="module-card">
                    <div class="core-metrics-grid">
                        <div class="metric-item">
                            <div class="label">市值</div>
                            <div class="value">{{market_cap}}</div>
                        </div>
                        <div class="metric-item">
                            <div class="label">24h交易量</div>
                            <div class="value">{{volume_24h}}</div>
                        </div>
                        <div class="metric-item">
                            <div class="label">流动性</div>
                            <div class="value">{{liquidity}}</div>
                        </div>
                        <div class="metric-item">
                            <div class="label">交易对数</div>
                            <div class="value">{{pairs_count}}</div>
                        </div>
                    </div>
                </div>
                <div class="module-card stretchy chart-module">
                    <h2 class="module-title">📈 价格走势图 (24H)</h2>
                    <div class="chart-container">
                        <div id="chart_container"></div>
                    </div>
                </div>
            </div>
            <!-- RIGHT COLUMN -->
            <div class="right-column">
                <div class="module-card table-module">
                    <h2 class="module-title">💰 主要交易对</h2>
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr><th>交易对</th><th>价格</th><th>24h%</th><th>交易量</th></tr>
                            </thead>
                            <tbody>{{pairs_table}}</tbody>
                        </table>
                    </div>
                </div>
                <div class="module-card table-module">
                    <h2 class="module-title">💧 流动性分布</h2>
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr><th>交易对</th><th>流动性</th><th>占比</th></tr>
                            </thead>
                            <tbody>{{liquidity_table}}</tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
        <footer class="footer">
            PYTHIA AI 增强分析工具 v3.0 | "数据驱动决策，理性投资未来"
        </footer>
    </div>
    {{js_content}}
</body>
</html>'''
        
        template_path = self.templates_dir / "main_report.html"
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(template_content)
    
    def _create_error_template(self):
        """创建错误页面模板"""
        template_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=1280, initial-scale=1.0">
    <title>{{title}}</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: #0D1117;
            color: #E6EDF3;
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .error-container {
            background: #161B22;
            border: 1px solid #30363D;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            max-width: 600px;
        }
        .error-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }
        .error-title {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #F59E0B;
        }
        .error-message {
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: #B0BAC6;
        }
        .retry-info {
            background: #0D1117;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            font-size: 0.9rem;
            color: #7D8590;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">{{error_icon}}</div>
        <h1 class="error-title">{{error_title}}</h1>
        <p class="error-message">{{error_message}}</p>
        <div class="retry-info">
            <strong>生成时间:</strong> {{timestamp}}<br>
            <strong>状态:</strong> {{status}}<br>
            <strong>建议:</strong> {{suggestion}}
        </div>
    </div>
</body>
</html>'''
        
        template_path = self.templates_dir / "error_page.html"
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(template_content)
    
    def _create_css_template(self):
        """创建CSS样式模板"""
        css_content = ''':root {
            --bg-primary: #0D1117;
            --bg-secondary: #101419;
            --bg-module: #161B22;
            --text-primary: #E6EDF3;
            --text-secondary: #B0BAC6;
            --text-muted: #7D8590;
            --accent-primary: #F2BC8C;
            --border-primary: #30363D;
            --border-secondary: #21262D;
            --success: #28A745;
            --danger: #F59E0B;

            /* 字体大小变量 - 弹性计算 */
            --base-font-size: 1rem;
            --small-font-size: 0.85rem;
            --medium-font-size: 1.1rem;
            --large-font-size: 1.3rem;
            --xlarge-font-size: 1.6rem;
            --xxlarge-font-size: 2rem;

            /* 间距变量 */
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 0.75rem;
            --spacing-lg: 1rem;
            --spacing-xl: 1.5rem;
        }
        *, *::before, *::after {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        body, html {
            font-family: 'Noto Sans SC', sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: var(--base-font-size);
            line-height: 1.4;
            -webkit-font-smoothing: antialiased;
            overflow: hidden;
            margin: 0;
            padding: 0;
            width: 100vw;
            height: 100vh;
        }
        .report-container {
            width: 1280px;
            height: 720px;
            background: var(--bg-primary);
            display: flex;
            flex-direction: column;
            margin: 0 auto;
            position: relative;
        }
        .report-header {
            padding: 8px 20px;
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--border-primary);
            flex-shrink: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 50px;
        }
        .header-title h1 {
            font-family: 'Noto Sans SC', sans-serif;
            font-size: var(--xlarge-font-size);
            font-weight: 700;
        }
        .header-title h1 .logo {
            font-family: 'JetBrains Mono', monospace;
            margin-right: 6px;
            color: var(--accent-primary);
        }
        .header-meta {
            background-color: #0D1117;
            border-radius: 12px;
            padding: var(--spacing-xs) var(--spacing-md);
            font-size: var(--small-font-size);
            color: var(--text-muted);
            font-family: 'JetBrains Mono', monospace;
        }
        .header-meta strong {
            color: var(--text-secondary);
        }
        .report-main {
            flex-grow: 1;
            padding: 10px;
            display: flex;
            gap: 10px;
            overflow: hidden;
            height: calc(720px - 50px - 25px);
        }
        .left-column {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
            height: 100%;
        }
        .center-column {
            flex: 2;
            display: flex;
            flex-direction: column;
            gap: 8px;
            height: 100%;
        }
        .right-column {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
            height: 100%;
        }
        .module-card {
            background: var(--bg-module);
            border-radius: 6px;
            border: 1px solid var(--border-primary);
            padding: 12px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        .module-card .module-title {
            font-size: var(--medium-font-size);
            font-weight: 500;
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            flex-shrink: 0;
        }
        .module-card.stretchy {
            flex-grow: 1;
            min-height: 0;
        }
        .analysis-list {
            list-style: none;
            font-size: var(--base-font-size);
            padding-left: var(--spacing-xs);
            overflow: hidden;
            flex-grow: 1;
        }
        .analysis-list li {
            margin-bottom: var(--spacing-sm);
            line-height: 1.3;
        }
        .enhanced-metrics-table {
            width: 100%;
            border-collapse: collapse;
            flex-grow: 1;
        }
        .enhanced-metrics-table td {
            padding: var(--spacing-sm) var(--spacing-xs);
            border-bottom: 1px solid var(--border-secondary);
            font-size: var(--base-font-size);
            vertical-align: middle;
        }
        .enhanced-metrics-table tr:last-child td {
            border-bottom: none;
            padding-bottom: 0;
        }
        .enhanced-metrics-table tr:first-child td {
            padding-top: 0;
        }
        .enhanced-metrics-table .metric-label {
            color: var(--text-muted);
            line-height: 1.2;
        }
        .enhanced-metrics-table .metric-value {
            font-weight: 500;
            text-align: right;
            font-family: 'JetBrains Mono', monospace;
            color: var(--text-primary);
        }
        .core-metrics-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 0;
        }
        .metric-item {
            background-color: var(--bg-secondary);
            border-radius: 4px;
            padding: var(--spacing-md);
            text-align: center;
            border: 1px solid var(--border-secondary);
        }
        .metric-item .label {
            font-size: var(--small-font-size);
            color: var(--text-muted);
            margin-bottom: var(--spacing-xs);
            display: block;
        }
        .metric-item .value {
            font-size: var(--large-font-size);
            font-weight: 700;
            font-family: 'JetBrains Mono', monospace;
        }
        .chart-module {
            flex-grow: 1;
            padding: 12px;
            min-height: 0;
        }
        .chart-container {
            flex-grow: 1;
            min-height: 0;
            height: 100%;
        }
        #chart_container {
            width: 100%;
            height: 100%;
        }
        .table-module {
            flex-grow: 1;
            min-height: 0;
            overflow: hidden;
        }
        .table-container {
            overflow-y: auto;
            flex-grow: 1;
            max-height: 100%;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: var(--small-font-size);
        }
        .data-table th {
            background-color: var(--bg-secondary);
            color: var(--text-muted);
            font-weight: 500;
            padding: var(--spacing-sm);
            text-align: left;
            border-bottom: 1px solid var(--border-primary);
            position: sticky;
            top: 0;
            z-index: 1;
        }
        .data-table td {
            padding: var(--spacing-sm);
            border-bottom: 1px solid var(--border-secondary);
            vertical-align: middle;
        }
        .data-table tr:hover {
            background-color: var(--bg-secondary);
        }
        .footer {
            padding: 8px 20px;
            background: var(--bg-secondary);
            border-top: 1px solid var(--border-primary);
            text-align: center;
            font-size: var(--small-font-size);
            color: var(--text-muted);
            font-family: 'JetBrains Mono', monospace;
            flex-shrink: 0;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
        }'''
        
        template_path = self.templates_dir / "styles.css"
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(css_content)
    
    def _create_js_template(self):
        """创建JavaScript模板"""
        js_content = '''<!-- TradingView Widget Script - 专业版本 -->
    <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
    <script type="text/javascript">
        // 确保TradingView全局变量可用，兼容html_to_image_headless.py
        window.TradingView = window.TradingView || {};

        // 图表加载状态标记
        window.chartLoaded = false;
        window.tradingViewLoaded = false;

        function initializeTradingViewChart() {
            try {
                // 检查容器是否存在
                const container = document.getElementById('chart_container');
                if (!container) {
                    console.error('Chart container not found');
                    return;
                }

                // 标记TradingView已加载
                window.tradingViewLoaded = true;

                // 创建TradingView图表 - 使用专业配置
                const widget = new TradingView.widget({
                    "autosize": true,
                    "symbol": "CRYPTO:PYTHIAUSD",
                    "interval": "30",
                    "theme": "dark",
                    "style": "1",
                    "locale": "zh_CN",
                    "toolbar_bg": "#161B22",
                    "enable_publishing": false,
                    "hide_top_toolbar": true,
                    "hide_side_toolbar": true,
                    "hide_legend": true,
                    "container_id": "chart_container",
                    "overrides": {
                        "paneProperties.background": "#161B22",
                        "paneProperties.vertGridProperties.color": "rgba(255,255,255,0.05)",
                        "paneProperties.horzGridProperties.color": "rgba(255,255,255,0.05)",
                        "mainSeriesProperties.candleStyle.upColor": "#28A745",
                        "mainSeriesProperties.candleStyle.downColor": "#DC3545",
                        "mainSeriesProperties.candleStyle.borderUpColor": "#28A745",
                        "mainSeriesProperties.candleStyle.borderDownColor": "#DC3545",
                        "scalesProperties.textColor": "#7D8590"
                    },
                    "onChartReady": function() {
                        // 图表准备就绪
                        window.chartLoaded = true;
                        console.log('TradingView chart loaded successfully');
                    }
                });

                // 备用标记，防止onChartReady不触发
                setTimeout(function() {
                    window.chartLoaded = true;
                }, 5000);

            } catch (error) {
                console.error('TradingView chart initialization error:', error);
                // 即使出错也标记为已加载，避免无限等待
                window.chartLoaded = true;
                window.tradingViewLoaded = true;
            }
        }

        // 页面加载完成后初始化图表
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeTradingViewChart);
        } else {
            initializeTradingViewChart();
        }

        // 窗口加载完成后的备用初始化
        window.addEventListener('load', function() {
            setTimeout(function() {
                if (!window.tradingViewLoaded) {
                    initializeTradingViewChart();
                }
                // 最终备用标记
                setTimeout(function() {
                    window.chartLoaded = true;
                    window.tradingViewLoaded = true;
                }, 3000);
            }, 1000);
        });
    </script>'''
        
        template_path = self.templates_dir / "chart_script.js"
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(js_content)

    def load_template(self, template_name: str) -> str:
        """加载指定的模板文件"""
        template_path = self.templates_dir / f"{template_name}.html"
        if not template_path.exists():
            raise FileNotFoundError(f"模板文件不存在: {template_path}")

        with open(template_path, 'r', encoding='utf-8') as f:
            return f.read()

    def load_css(self) -> str:
        """加载CSS样式"""
        css_path = self.templates_dir / "styles.css"
        if not css_path.exists():
            return ""

        with open(css_path, 'r', encoding='utf-8') as f:
            return f.read()

    def load_js(self) -> str:
        """加载JavaScript代码"""
        js_path = self.templates_dir / "chart_script.js"
        if not js_path.exists():
            return ""

        with open(js_path, 'r', encoding='utf-8') as f:
            return f.read()

    def render_template(self, template_name: str, data: Dict[str, Any]) -> str:
        """渲染模板，填充数据"""
        template_content = self.load_template(template_name)

        # 如果是主报告模板，需要加载CSS和JS
        if template_name == "main_report":
            css_content = self.load_css()
            js_content = self.load_js()
            data['css_content'] = css_content
            data['js_content'] = js_content

        # 简单的模板变量替换
        for key, value in data.items():
            placeholder = f"{{{{{key}}}}}"
            template_content = template_content.replace(placeholder, str(value))

        return template_content

    def generate_professional_report(self, report_data: Dict[str, Any]) -> str:
        """生成专业报告HTML"""
        # 准备模板数据
        template_data = {
            'title': 'PYTHIA 分析报告 (最终复刻版)',
            'token_address': report_data.get('token_address', '')[:12] + '...',
            'chain': 'Solana',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M'),
            'positive_factors': report_data.get('positive_factors', ''),
            'avg_price_change': f"{report_data.get('avg_price_change', 0):.2f}",
            'buy_sell_ratio': f"{report_data.get('buy_sell_ratio', 0):.2f}",
            'sentiment': report_data.get('sentiment', '😐 中性'),
            'volume_to_mcap': f"{report_data.get('volume_to_mcap', 0):.1f}",
            'market_cap': f"${report_data.get('total_market_cap', 0)/1000000:.1f}M",
            'volume_24h': f"${report_data.get('total_volume', 0)/1000000:.1f}M",
            'liquidity': f"${report_data.get('total_liquidity', 0)/1000000:.1f}M",
            'pairs_count': str(report_data.get('pairs_count', 0)),
            'pairs_table': report_data.get('pairs_table', ''),
            'liquidity_table': report_data.get('liquidity_table', '')
        }

        return self.render_template('main_report', template_data)

    def generate_error_report(self, error_type: str = "network") -> str:
        """生成错误报告HTML"""
        error_configs = {
            "network": {
                "error_icon": "🔌",
                "error_title": "网络连接异常",
                "error_message": "无法连接到DexScreener API服务器。<br>这可能是由于网络连接问题或API服务暂时不可用。",
                "status": "自动重试机制已启用",
                "suggestion": "系统将在下个周期自动重试"
            },
            "data": {
                "error_icon": "📊",
                "error_title": "数据获取失败",
                "error_message": "无法获取有效的市场数据。<br>请稍后重试或检查代币地址是否正确。",
                "status": "数据源暂时不可用",
                "suggestion": "请稍后重试"
            },
            "general": {
                "error_icon": "⚠️",
                "error_title": "系统异常",
                "error_message": "系统遇到未知错误。<br>请联系技术支持或稍后重试。",
                "status": "错误已记录",
                "suggestion": "请联系技术支持"
            }
        }

        config = error_configs.get(error_type, error_configs["general"])
        template_data = {
            'title': 'PYTHIA 分析报告 - 错误',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            **config
        }

        return self.render_template('error_page', template_data)
