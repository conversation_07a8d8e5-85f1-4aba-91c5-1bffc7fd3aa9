#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
无头浏览器HTML转图片工具
使用Selenium + Chrome无头模式，不依赖Playwright
支持Telegram群组发送和冷却时间
"""

import os
import sys
import time
import requests
from pathlib import Path
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

# 导入配置文件
try:
    from config import OUTPUT_CONFIG
    TELEGRAM_BOT_TOKEN = OUTPUT_CONFIG["telegram"]["bot_token"]
    TELEGRAM_CHAT_ID = OUTPUT_CONFIG["telegram"]["chat_id"]
except ImportError:
    print("⚠️ 无法导入config.py配置文件")
    TELEGRAM_BOT_TOKEN = None
    TELEGRAM_CHAT_ID = None
except KeyError as e:
    print(f"⚠️ 配置文件中缺少Telegram配置项: {e}")
    TELEGRAM_BOT_TOKEN = None
    TELEGRAM_CHAT_ID = None

class HeadlessHTMLConverter:
    def __init__(self):
        self.output_dir = Path("images")
        self.output_dir.mkdir(exist_ok=True)
        self.last_process_time = None  # 记录上次处理时间
    
    def setup_driver(self, width=None, height=None):
        """设置无头Chrome浏览器"""
        chrome_options = Options()
        chrome_options.add_argument('--headless=new')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--hide-scrollbars')
        chrome_options.add_argument('--enable-javascript')  # 启用JavaScript以加载图表
        chrome_options.add_argument('--mute-audio')
        chrome_options.add_argument('--force-device-scale-factor=1')
        chrome_options.add_argument('--disable-infobars')
        chrome_options.add_argument('--disable-notifications')
        chrome_options.add_argument('--disable-popup-blocking')
        
        # 使用传入的尺寸或默认尺寸
        w = width or 1920
        h = height or 1080
        chrome_options.add_argument(f'--window-size={w},{h}')
        
        # 自动下载并设置ChromeDriver
        service = Service(ChromeDriverManager().install())
        
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.set_window_size(w, h)
        
        return driver
    
    def wait_for_chart_loading(self, driver):
        """等待TradingView图表完全加载"""
        try:
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.webdriver.common.by import By
            
            wait = WebDriverWait(driver, 30)
            
            # 1. 等待TradingView脚本加载
            print("⏳ 等待TradingView脚本...")
            driver.execute_script("""
                var checkTradingView = function() {
                    return typeof TradingView !== 'undefined';
                };
                
                var startTime = Date.now();
                var checkInterval = setInterval(function() {
                    if (checkTradingView() || Date.now() - startTime > 20000) {
                        clearInterval(checkInterval);
                        window.tradingViewLoaded = checkTradingView();
                    }
                }, 500);
            """)
            
            # 等待脚本检查完成
            WebDriverWait(driver, 25).until(
                lambda d: d.execute_script("return typeof window.tradingViewLoaded !== 'undefined'")
            )
            
            tv_loaded = driver.execute_script("return window.tradingViewLoaded")
            if tv_loaded:
                print("✅ TradingView脚本已加载")
            else:
                print("⚠️ TradingView脚本加载超时")
            
            # 2. 等待图表容器出现
            print("⏳ 等待图表容器...")
            try:
                wait.until(EC.presence_of_element_located((By.ID, "chart_container")))
                print("✅ 图表容器已找到")
            except:
                print("⚠️ 图表容器未找到，继续等待...")
            
            # 3. 等待图表内容渲染 - 检查iframe或canvas
            print("⏳ 等待图表内容渲染...")
            chart_rendered = False
            
            for attempt in range(20):  # 最多等待20秒
                try:
                    # 检查是否有图表内容
                    has_chart = driver.execute_script("""
                        var container = document.getElementById('chart_container');
                        if (!container) return false;
                        
                        // 检查iframe（TradingView常用）
                        var iframe = container.querySelector('iframe');
                        if (iframe) return true;
                        
                        // 检查canvas元素
                        var canvas = container.querySelector('canvas');
                        if (canvas) return true;
                        
                        // 检查是否有实际内容
                        return container.children.length > 0 && container.offsetHeight > 100;
                    """)
                    
                    if has_chart:
                        print("✅ 图表内容已渲染")
                        chart_rendered = True
                        break
                    
                    time.sleep(1)
                    
                except Exception as e:
                    print(f"⚠️ 图表检测异常: {e}")
                    break
            
            if not chart_rendered:
                print("⚠️ 图表渲染检测超时，继续处理...")
            
            # 4. 深度检查图表是否完全渲染
            print("🔍 深度检查图表渲染状态...")
            self.verify_chart_fully_rendered(driver)
            
            # 5. 额外等待确保图表完全加载
            print("⏳ 最终等待图表稳定...")
            time.sleep(8)  # 给图表一些时间完全渲染
            print("✅ 图表加载等待完成")
            
        except Exception as e:
            print(f"⚠️ 图表加载等待失败: {e}")
            print("🔄 使用备用等待策略...")
            time.sleep(15)  # 备用等待时间
    
    def verify_chart_fully_rendered(self, driver):
        """深度验证图表是否完全渲染"""
        try:
            print("📊 验证图表完全渲染...")
            
            # 多重检查确保图表完全加载
            for check_round in range(3):  # 进行3轮检查
                print(f"🔄 第 {check_round + 1}/3 轮渲染检查...")
                
                chart_status = driver.execute_script("""
                    var status = {
                        hasContainer: false,
                        hasIframe: false,
                        hasCanvas: false,
                        hasChartData: false,
                        containerSize: {width: 0, height: 0},
                        iframeLoaded: false,
                        canvasDrawn: false,
                        networkIdle: false
                    };
                    
                    // 检查容器
                    var container = document.getElementById('chart_container');
                    if (container) {
                        status.hasContainer = true;
                        status.containerSize.width = container.offsetWidth;
                        status.containerSize.height = container.offsetHeight;
                        
                        // 检查iframe
                        var iframe = container.querySelector('iframe');
                        if (iframe) {
                            status.hasIframe = true;
                            try {
                                // 检查iframe是否加载完成
                                if (iframe.contentDocument || iframe.contentWindow) {
                                    status.iframeLoaded = true;
                                }
                            } catch(e) {
                                // 跨域iframe，假设已加载
                                status.iframeLoaded = true;
                            }
                        }
                        
                        // 检查canvas
                        var canvases = container.querySelectorAll('canvas');
                        if (canvases.length > 0) {
                            status.hasCanvas = true;
                            
                            // 检查canvas是否有绘制内容
                            for (var i = 0; i < canvases.length; i++) {
                                var canvas = canvases[i];
                                if (canvas.width > 0 && canvas.height > 0) {
                                    try {
                                        var ctx = canvas.getContext('2d');
                                        var imageData = ctx.getImageData(0, 0, Math.min(canvas.width, 100), Math.min(canvas.height, 100));
                                        var data = imageData.data;
                                        
                                        // 检查是否有非透明像素
                                        for (var j = 3; j < data.length; j += 4) {
                                            if (data[j] > 0) {  // alpha > 0
                                                status.canvasDrawn = true;
                                                break;
                                            }
                                        }
                                        if (status.canvasDrawn) break;
                                    } catch(e) {
                                        // 无法访问canvas内容，假设已绘制
                                        status.canvasDrawn = true;
                                        break;
                                    }
                                }
                            }
                        }
                        
                        // 检查是否有图表数据元素
                        var dataElements = container.querySelectorAll('[class*="chart"], [class*="plot"], [class*="series"], [id*="chart"], [id*="plot"]');
                        if (dataElements.length > 0) {
                            status.hasChartData = true;
                        }
                    }
                    
                    // 检查网络请求是否完成（简单检查）
                    if (typeof performance !== 'undefined' && performance.getEntriesByType) {
                        var resources = performance.getEntriesByType('resource');
                        var pendingRequests = resources.filter(function(r) {
                            return r.responseEnd === 0;  // 未完成的请求
                        });
                        status.networkIdle = pendingRequests.length === 0;
                    }
                    
                    return status;
                """)
                
                # 打印检查结果
                print(f"   📦 容器: {'✅' if chart_status['hasContainer'] else '❌'} ({chart_status['containerSize']['width']}x{chart_status['containerSize']['height']})")
                print(f"   🖼️ iframe: {'✅' if chart_status['hasIframe'] else '❌'} (加载: {'✅' if chart_status['iframeLoaded'] else '❌'})")
                print(f"   🎨 Canvas: {'✅' if chart_status['hasCanvas'] else '❌'} (绘制: {'✅' if chart_status['canvasDrawn'] else '❌'})")
                print(f"   📊 图表数据: {'✅' if chart_status['hasChartData'] else '❌'}")
                print(f"   🌐 网络空闲: {'✅' if chart_status['networkIdle'] else '❌'}")
                
                # 判断图表是否完全渲染
                chart_ready = (
                    chart_status['hasContainer'] and
                    chart_status['containerSize']['width'] > 100 and
                    chart_status['containerSize']['height'] > 100 and
                    (chart_status['hasIframe'] or chart_status['hasCanvas']) and
                    (chart_status['iframeLoaded'] or chart_status['canvasDrawn'])
                )
                
                if chart_ready:
                    print(f"   ✅ 第 {check_round + 1} 轮检查通过 - 图表已完全渲染")
                    break
                else:
                    print(f"   ⏳ 第 {check_round + 1} 轮检查未通过 - 继续等待...")
                    time.sleep(3)  # 等待3秒后再次检查
            
            # 最终状态检查
            final_check = driver.execute_script("""
                var container = document.getElementById('chart_container');
                if (!container) return false;
                
                // 检查容器是否有足够的内容
                var hasContent = container.offsetWidth > 200 && container.offsetHeight > 200;
                var hasElements = container.children.length > 0;
                
                // 检查是否有可见的图表元素
                var visibleElements = 0;
                var allElements = container.querySelectorAll('*');
                for (var i = 0; i < allElements.length; i++) {
                    var el = allElements[i];
                    var style = window.getComputedStyle(el);
                    if (style.display !== 'none' && style.visibility !== 'hidden' && 
                        el.offsetWidth > 0 && el.offsetHeight > 0) {
                        visibleElements++;
                    }
                }
                
                return hasContent && hasElements && visibleElements > 5;
            """)
            
            if final_check:
                print("✅ 图表渲染验证通过 - 准备截图")
            else:
                print("⚠️ 图表渲染验证未完全通过 - 但继续截图")
                
        except Exception as e:
            print(f"⚠️ 图表渲染验证失败: {e}")
            print("🔄 跳过验证，继续截图...")
    
    def detect_content_size(self, driver):
        """智能检测页面内容的实际尺寸，去除多余空白"""
        try:
            # 更精确的内容尺寸检测
            dimensions = driver.execute_script("""
                // 获取所有可能的尺寸
                var body = document.body;
                var html = document.documentElement;
                
                // 获取实际内容区域
                var allElements = document.querySelectorAll('*');
                var maxRight = 0;
                var maxBottom = 0;
                var minLeft = window.innerWidth;
                var minTop = window.innerHeight;
                
                // 遍历所有元素找到实际内容边界
                for (var i = 0; i < allElements.length; i++) {
                    var el = allElements[i];
                    var rect = el.getBoundingClientRect();
                    
                    // 跳过不可见元素
                    if (rect.width === 0 || rect.height === 0) continue;
                    
                    maxRight = Math.max(maxRight, rect.right);
                    maxBottom = Math.max(maxBottom, rect.bottom);
                    minLeft = Math.min(minLeft, rect.left);
                    minTop = Math.min(minTop, rect.top);
                }
                
                // 计算实际内容尺寸
                var contentWidth = Math.max(maxRight - Math.max(0, minLeft), 1280);
                var contentHeight = Math.max(maxBottom - Math.max(0, minTop), 720);
                
                // 添加一些边距
                contentWidth = Math.min(contentWidth + 40, 1920);
                contentHeight = Math.min(contentHeight + 40, 1080);
                
                return {
                    width: Math.round(contentWidth),
                    height: Math.round(contentHeight),
                    bounds: {
                        left: minLeft,
                        top: minTop,
                        right: maxRight,
                        bottom: maxBottom
                    }
                };
            """)
            
            content_width = dimensions['width']
            content_height = dimensions['height']
            
            print(f"🔍 智能检测内容尺寸: {content_width}x{content_height}")
            print(f"📏 内容边界: left={dimensions['bounds']['left']:.1f}, right={dimensions['bounds']['right']:.1f}")
            
            return content_width, content_height
            
        except Exception as e:
            print(f"⚠️ 尺寸检测失败: {e}")
            return 1280, 720
    
    def convert_html_to_image(self, html_file_path, output_path=None, max_retries=3, auto_size=True):
        """转换HTML文件为图片，支持自动尺寸检测"""
        html_path = Path(html_file_path)
        if not html_path.exists():
            raise FileNotFoundError(f"HTML文件不存在: {html_file_path}")
        
        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = self.output_dir / f"{html_path.stem}_{timestamp}.png"
        
        print(f"📄 转换: {html_path.name}")
        print(f"📁 输出: {output_path}")
        
        for attempt in range(max_retries):
            driver = None
            try:
                print(f"🔄 尝试 {attempt + 1}/{max_retries}")
                
                # 第一步：用大窗口启动浏览器进行内容检测
                driver = self.setup_driver(1920, 1080)
                
                # 加载HTML文件
                file_url = f"file://{html_path.absolute()}"
                print(f"🔗 加载: {file_url}")
                
                driver.get(file_url)
                
                # 等待页面加载和JavaScript执行
                print("⏳ 等待页面基础渲染...")
                time.sleep(3)
                
                # 等待TradingView图表完全加载
                print("📊 等待TradingView图表完全加载...")
                self.wait_for_chart_loading(driver)
                
                if auto_size:
                    # 检测内容实际尺寸
                    content_width, content_height = self.detect_content_size(driver)
                    
                    # 关闭当前浏览器
                    driver.quit()
                    
                    # 用检测到的尺寸重新启动浏览器
                    print(f"🔄 使用检测尺寸重新启动浏览器: {content_width}x{content_height}")
                    driver = self.setup_driver(content_width, content_height)
                    
                    # 重新加载页面
                    driver.get(file_url)
                    time.sleep(3)
                    
                    # 重新等待图表完全加载
                    print("📊 重新等待图表完全加载...")
                    self.wait_for_chart_loading(driver)
                    
                    # 额外等待1分钟确保图表完全稳定
                    print("⏰ 额外等待1分钟确保图表完全稳定...")
                    time.sleep(60)
                    print("✅ 1分钟等待完成")
                
                # 截图
                print("📸 开始截图...")
                driver.save_screenshot(str(output_path))
                
                # 智能裁剪去除空白区域
                if auto_size:
                    try:
                        from PIL import Image
                        print("🔧 智能裁剪空白区域...")
                        
                        with Image.open(output_path) as img:
                            # 转换为RGB模式以便处理
                            if img.mode != 'RGB':
                                img = img.convert('RGB')
                            
                            # 获取图片尺寸
                            width, height = img.size
                            print(f"📐 原始尺寸: {width}x{height}")
                            
                            # 检测实际内容边界（非黑色区域）
                            pixels = img.load()
                            
                            # 找到左边界
                            left_bound = 0
                            for x in range(width):
                                has_content = False
                                for y in range(height):
                                    r, g, b = pixels[x, y]
                                    # 如果不是纯黑色或接近黑色
                                    if r > 30 or g > 30 or b > 30:
                                        has_content = True
                                        break
                                if has_content:
                                    left_bound = max(0, x - 10)  # 留一点边距
                                    break
                            
                            # 找到右边界
                            right_bound = width
                            for x in range(width-1, -1, -1):
                                has_content = False
                                for y in range(height):
                                    r, g, b = pixels[x, y]
                                    if r > 30 or g > 30 or b > 30:
                                        has_content = True
                                        break
                                if has_content:
                                    right_bound = min(width, x + 10)  # 留一点边距
                                    break
                            
                            # 找到上边界
                            top_bound = 0
                            for y in range(height):
                                has_content = False
                                for x in range(width):
                                    r, g, b = pixels[x, y]
                                    if r > 30 or g > 30 or b > 30:
                                        has_content = True
                                        break
                                if has_content:
                                    top_bound = max(0, y - 10)
                                    break
                            
                            # 找到下边界
                            bottom_bound = height
                            for y in range(height-1, -1, -1):
                                has_content = False
                                for x in range(width):
                                    r, g, b = pixels[x, y]
                                    if r > 30 or g > 30 or b > 30:
                                        has_content = True
                                        break
                                if has_content:
                                    bottom_bound = min(height, y + 10)
                                    break
                            
                            # 裁剪图片
                            if right_bound > left_bound and bottom_bound > top_bound:
                                cropped_img = img.crop((left_bound, top_bound, right_bound, bottom_bound))
                                cropped_img.save(output_path, 'PNG')
                                
                                new_width = right_bound - left_bound
                                new_height = bottom_bound - top_bound
                                print(f"✂️ 裁剪后尺寸: {new_width}x{new_height}")
                                print(f"📏 裁剪区域: ({left_bound}, {top_bound}) -> ({right_bound}, {bottom_bound})")
                            else:
                                print("⚠️ 未检测到有效内容区域，保持原图")
                            
                    except ImportError:
                        print("⚠️ PIL不可用，无法进行智能裁剪")
                    except Exception as e:
                        print(f"⚠️ 智能裁剪失败: {e}")
                
                else:
                    # 非自动尺寸模式，强制调整到1280x720
                    try:
                        from PIL import Image
                        with Image.open(output_path) as img:
                            width, height = img.size
                            print(f"📐 图片尺寸: {width}x{height}")
                            
                            if width != 1280 or height != 720:
                                print("🔧 调整图片尺寸到1280x720...")
                                resized_img = img.resize((1280, 720), Image.Resampling.LANCZOS)
                                resized_img.save(output_path, 'PNG')
                                print(f"✅ 已调整为: 1280x720")
                            
                    except ImportError:
                        print("⚠️ PIL不可用，无法调整图片尺寸")
                
                print(f"✅ 成功: {output_path}")
                return str(output_path)
                
            except Exception as e:
                print(f"❌ 第 {attempt + 1} 次失败: {e}")
                if attempt == max_retries - 1:
                    raise
                time.sleep(3)
                
            finally:
                if driver:
                    try:
                        driver.quit()
                    except:
                        pass
    
    def send_to_telegram(self, image_path, html_filename):
        """发送图片到Telegram群组"""
        try:
            if TELEGRAM_BOT_TOKEN == "YOUR_BOT_TOKEN_HERE" or TELEGRAM_CHAT_ID == "YOUR_CHAT_ID_HERE":
                print("⚠️ Telegram配置未设置，跳过发送")
                return False
            
            print("📤 发送图片到Telegram群组...")
            
            # 准备发送的消息
            caption = f"📊 Pythia分析图表\n📅 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n📄 源文件: {html_filename}"
            
            # 发送图片
            url = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendPhoto"
            
            with open(image_path, 'rb') as photo:
                files = {'photo': photo}
                data = {
                    'chat_id': TELEGRAM_CHAT_ID,
                    'caption': caption,
                    'parse_mode': 'HTML'
                }
                
                response = requests.post(url, files=files, data=data, timeout=30)
                
            if response.status_code == 200:
                print("✅ 图片已成功发送到Telegram群组")
                return True
            else:
                print(f"❌ Telegram发送失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Telegram发送异常: {e}")
            return False
    
    def check_cooldown(self):
        """检查是否在冷却时间内"""
        if self.last_process_time is None:
            return False
        
        cooldown_minutes = 50
        time_since_last = datetime.now() - self.last_process_time
        cooldown_remaining = timedelta(minutes=cooldown_minutes) - time_since_last
        
        if cooldown_remaining.total_seconds() > 0:
            remaining_minutes = int(cooldown_remaining.total_seconds() / 60)
            remaining_seconds = int(cooldown_remaining.total_seconds() % 60)
            print(f"❄️ 冷却中... 还需等待 {remaining_minutes}分{remaining_seconds}秒")
            return True
        
        return False
    
    def find_latest_html(self):
        """查找最新的HTML文件"""
        data_dir = Path("data")
        if not data_dir.exists():
            print("❌ data目录不存在")
            return None
        
        html_files = list(data_dir.glob("*.html"))
        if not html_files:
            print("❌ 没有找到HTML文件")
            return None
        
        latest_file = max(html_files, key=lambda f: f.stat().st_mtime)
        mod_time = datetime.fromtimestamp(latest_file.stat().st_mtime)
        
        print(f"📁 最新: {latest_file.name}")
        print(f"📅 时间: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        return latest_file

def main():
    """主函数 - 持续监控模式"""
    converter = HeadlessHTMLConverter()
    
    print("🐭 无头Chrome HTML转图片 - 持续监控模式")
    print("=" * 50)
    print("🔄 程序将持续运行，监控新的HTML文件...")
    print("💡 按 Ctrl+C 退出程序")
    print("=" * 50)
    
    processed_files = set()  # 记录已处理的文件
    
    try:
        while True:
            try:
                # 查找最新HTML文件
                latest_file = converter.find_latest_html()
                
                if latest_file and str(latest_file) not in processed_files:
                    # 检查冷却时间
                    if converter.check_cooldown():
                        print(f"⏰ 跳过处理 {latest_file.name} - 仍在冷却期")
                    else:
                        print(f"\n🆕 发现新文件: {latest_file.name}")
                        print("-" * 30)
                        
                        try:
                            result = converter.convert_html_to_image(str(latest_file))
                            if result:
                                print(f"\n🎉 转换完成! 图片: {result}")
                                processed_files.add(str(latest_file))
                                
                                # 发送到Telegram群组
                                converter.send_to_telegram(result, latest_file.name)
                                
                                # 记录处理时间并开始冷却
                                converter.last_process_time = datetime.now()
                                print(f"\n❄️ 开始50分钟冷却期...")
                                print(f"🕐 下次可处理时间: {(converter.last_process_time + timedelta(minutes=50)).strftime('%H:%M:%S')}")
                                
                            else:
                                print(f"\n❌ 转换失败")
                                processed_files.add(str(latest_file))
                        except Exception as e:
                            print(f"\n❌ 转换错误: {e}")
                            # 即使失败也标记为已处理，避免重复尝试
                            processed_files.add(str(latest_file))
                
                # 等待10秒后再次检查
                print(f"\n⏰ 等待新文件... (已处理 {len(processed_files)} 个文件)")
                time.sleep(10)
                
            except KeyboardInterrupt:
                print("\n\n👋 用户中断，程序退出")
                break
            except Exception as e:
                print(f"\n⚠️ 监控异常: {e}")
                print("🔄 5秒后继续监控...")
                time.sleep(5)
                
    except KeyboardInterrupt:
        print("\n\n👋 程序已退出")

if __name__ == "__main__":
    main()