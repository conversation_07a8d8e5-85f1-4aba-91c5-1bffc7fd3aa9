#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PYTHIA代币全面分析与报告发送器 - 集成版本
集成了数据分析、报告生成、HTML转图片和Telegram发送功能的完整解决方案
"""

import os
import asyncio
import time
import requests
import json
import threading
import schedule
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import config
from config import PYTHIA_TOKEN_INFO, API_CONFIG, ANALYSIS_CONFIG, SEARCH_KEYWORDS, OUTPUT_CONFIG

class HeadlessHTMLConverter:
    """无头浏览器HTML转图片工具"""

    def __init__(self):
        self.output_dir = Path("images")
        self.output_dir.mkdir(exist_ok=True)
        self.last_process_time = None  # 记录上次处理时间

    def setup_driver(self, width=None, height=None):
        """设置无头Chrome浏览器"""
        chrome_options = Options()
        chrome_options.add_argument('--headless=new')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--hide-scrollbars')
        chrome_options.add_argument('--enable-javascript')  # 启用JavaScript以加载图表
        chrome_options.add_argument('--mute-audio')
        chrome_options.add_argument('--force-device-scale-factor=1')
        chrome_options.add_argument('--disable-infobars')
        chrome_options.add_argument('--disable-notifications')
        chrome_options.add_argument('--disable-popup-blocking')

        # 使用传入的尺寸或默认尺寸
        w = width or 1920
        h = height or 1080
        chrome_options.add_argument(f'--window-size={w},{h}')

        # 自动下载并设置ChromeDriver
        service = Service(ChromeDriverManager().install())

        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.set_window_size(w, h)

        return driver

    def wait_for_chart_loading(self, driver):
        """等待TradingView图表完全加载"""
        try:
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.webdriver.common.by import By

            wait = WebDriverWait(driver, 30)

            # 1. 等待TradingView脚本加载
            print("⏳ 等待TradingView脚本...")
            driver.execute_script("""
                var checkTradingView = function() {
                    return typeof TradingView !== 'undefined';
                };

                var startTime = Date.now();
                var checkInterval = setInterval(function() {
                    if (checkTradingView() || Date.now() - startTime > 20000) {
                        clearInterval(checkInterval);
                        window.tradingViewLoaded = checkTradingView();
                    }
                }, 500);
            """)

            # 等待脚本检查完成
            WebDriverWait(driver, 25).until(
                lambda d: d.execute_script("return typeof window.tradingViewLoaded !== 'undefined'")
            )

            tv_loaded = driver.execute_script("return window.tradingViewLoaded")
            if tv_loaded:
                print("✅ TradingView脚本已加载")
            else:
                print("⚠️ TradingView脚本加载超时")

            # 2. 等待图表容器出现
            print("⏳ 等待图表容器...")
            try:
                wait.until(EC.presence_of_element_located((By.ID, "chart_container")))
                print("✅ 图表容器已找到")
            except:
                print("⚠️ 图表容器未找到，继续等待...")

            # 3. 等待图表内容渲染 - 检查iframe或canvas
            print("⏳ 等待图表内容渲染...")
            chart_rendered = False

            for attempt in range(20):  # 最多等待20秒
                try:
                    # 检查是否有图表内容
                    has_chart = driver.execute_script("""
                        var container = document.getElementById('chart_container');
                        if (!container) return false;

                        // 检查iframe（TradingView常用）
                        var iframe = container.querySelector('iframe');
                        if (iframe) return true;

                        // 检查canvas元素
                        var canvas = container.querySelector('canvas');
                        if (canvas) return true;

                        // 检查是否有实际内容
                        return container.children.length > 0 && container.offsetHeight > 100;
                    """)

                    if has_chart:
                        print("✅ 图表内容已渲染")
                        chart_rendered = True
                        break

                    time.sleep(1)

                except Exception as e:
                    print(f"⚠️ 图表检测异常: {e}")
                    break

            if not chart_rendered:
                print("⚠️ 图表渲染检测超时，继续处理...")

            # 4. 额外等待确保图表完全加载
            print("⏳ 最终等待图表稳定...")
            time.sleep(8)  # 给图表一些时间完全渲染
            print("✅ 图表加载等待完成")

        except Exception as e:
            print(f"⚠️ 图表加载等待失败: {e}")
            print("🔄 使用备用等待策略...")
            time.sleep(15)  # 备用等待时间

    def detect_content_size(self, driver):
        """智能检测页面内容的实际尺寸，去除多余空白"""
        try:
            # 更精确的内容尺寸检测
            dimensions = driver.execute_script("""
                // 获取所有可能的尺寸
                var body = document.body;
                var html = document.documentElement;

                // 获取实际内容区域
                var allElements = document.querySelectorAll('*');
                var maxRight = 0;
                var maxBottom = 0;
                var minLeft = window.innerWidth;
                var minTop = window.innerHeight;

                // 遍历所有元素找到实际内容边界
                for (var i = 0; i < allElements.length; i++) {
                    var el = allElements[i];
                    var rect = el.getBoundingClientRect();

                    // 跳过不可见元素
                    if (rect.width === 0 || rect.height === 0) continue;

                    maxRight = Math.max(maxRight, rect.right);
                    maxBottom = Math.max(maxBottom, rect.bottom);
                    minLeft = Math.min(minLeft, rect.left);
                    minTop = Math.min(minTop, rect.top);
                }

                // 计算实际内容尺寸
                var contentWidth = Math.max(maxRight - Math.max(0, minLeft), 1280);
                var contentHeight = Math.max(maxBottom - Math.max(0, minTop), 720);

                // 添加一些边距
                contentWidth = Math.min(contentWidth + 40, 1920);
                contentHeight = Math.min(contentHeight + 40, 1080);

                return {
                    width: Math.round(contentWidth),
                    height: Math.round(contentHeight),
                    bounds: {
                        left: minLeft,
                        top: minTop,
                        right: maxRight,
                        bottom: maxBottom
                    }
                };
            """)

            content_width = dimensions['width']
            content_height = dimensions['height']

            print(f"🔍 智能检测内容尺寸: {content_width}x{content_height}")
            print(f"📏 内容边界: left={dimensions['bounds']['left']:.1f}, right={dimensions['bounds']['right']:.1f}")

            return content_width, content_height

        except Exception as e:
            print(f"⚠️ 尺寸检测失败: {e}")
            return 1280, 720

    def convert_html_to_image(self, html_file_path, output_path=None, max_retries=3, auto_size=True):
        """转换HTML文件为图片，支持自动尺寸检测"""
        html_path = Path(html_file_path)
        if not html_path.exists():
            raise FileNotFoundError(f"HTML文件不存在: {html_file_path}")

        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = self.output_dir / f"{html_path.stem}_{timestamp}.png"

        print(f"📄 转换: {html_path.name}")
        print(f"📁 输出: {output_path}")

        for attempt in range(max_retries):
            driver = None
            try:
                print(f"🔄 尝试 {attempt + 1}/{max_retries}")

                # 第一步：用大窗口启动浏览器进行内容检测
                driver = self.setup_driver(1920, 1080)

                # 加载HTML文件
                file_url = f"file://{html_path.absolute()}"
                print(f"🔗 加载: {file_url}")

                driver.get(file_url)

                # 等待页面加载和JavaScript执行
                print("⏳ 等待页面基础渲染...")
                time.sleep(3)

                # 等待TradingView图表完全加载
                print("📊 等待TradingView图表完全加载...")
                self.wait_for_chart_loading(driver)

                if auto_size:
                    # 检测内容实际尺寸
                    content_width, content_height = self.detect_content_size(driver)

                    # 关闭当前浏览器
                    driver.quit()

                    # 用检测到的尺寸重新启动浏览器
                    print(f"🔄 使用检测尺寸重新启动浏览器: {content_width}x{content_height}")
                    driver = self.setup_driver(content_width, content_height)

                    # 重新加载页面
                    driver.get(file_url)
                    time.sleep(3)

                    # 重新等待图表完全加载
                    print("📊 重新等待图表完全加载...")
                    self.wait_for_chart_loading(driver)

                    # 额外等待1分钟确保图表完全稳定
                    print("⏰ 额外等待1分钟确保图表完全稳定...")
                    time.sleep(60)
                    print("✅ 1分钟等待完成")

                # 截图
                print("📸 开始截图...")
                driver.save_screenshot(str(output_path))

                # 智能裁剪去除空白区域
                if auto_size:
                    try:
                        from PIL import Image
                        print("🔧 智能裁剪空白区域...")

                        with Image.open(output_path) as img:
                            # 转换为RGB模式以便处理
                            if img.mode != 'RGB':
                                img = img.convert('RGB')

                            # 获取图片尺寸
                            width, height = img.size
                            print(f"📐 原始尺寸: {width}x{height}")

                            # 检测实际内容边界（非黑色区域）
                            pixels = img.load()

                            # 找到左边界
                            left_bound = 0
                            for x in range(width):
                                has_content = False
                                for y in range(height):
                                    r, g, b = pixels[x, y]
                                    # 如果不是纯黑色或接近黑色
                                    if r > 30 or g > 30 or b > 30:
                                        has_content = True
                                        break
                                if has_content:
                                    left_bound = max(0, x - 10)  # 留一点边距
                                    break

                            # 找到右边界
                            right_bound = width
                            for x in range(width-1, -1, -1):
                                has_content = False
                                for y in range(height):
                                    r, g, b = pixels[x, y]
                                    if r > 30 or g > 30 or b > 30:
                                        has_content = True
                                        break
                                if has_content:
                                    right_bound = min(width, x + 10)  # 留一点边距
                                    break

                            # 找到上边界
                            top_bound = 0
                            for y in range(height):
                                has_content = False
                                for x in range(width):
                                    r, g, b = pixels[x, y]
                                    if r > 30 or g > 30 or b > 30:
                                        has_content = True
                                        break
                                if has_content:
                                    top_bound = max(0, y - 10)
                                    break

                            # 找到下边界
                            bottom_bound = height
                            for y in range(height-1, -1, -1):
                                has_content = False
                                for x in range(width):
                                    r, g, b = pixels[x, y]
                                    if r > 30 or g > 30 or b > 30:
                                        has_content = True
                                        break
                                if has_content:
                                    bottom_bound = min(height, y + 10)
                                    break

                            # 裁剪图片
                            if right_bound > left_bound and bottom_bound > top_bound:
                                cropped_img = img.crop((left_bound, top_bound, right_bound, bottom_bound))
                                cropped_img.save(output_path, 'PNG')

                                new_width = right_bound - left_bound
                                new_height = bottom_bound - top_bound
                                print(f"✂️ 裁剪后尺寸: {new_width}x{new_height}")
                                print(f"📏 裁剪区域: ({left_bound}, {top_bound}) -> ({right_bound}, {bottom_bound})")
                            else:
                                print("⚠️ 未检测到有效内容区域，保持原图")

                    except ImportError:
                        print("⚠️ PIL不可用，无法进行智能裁剪")
                    except Exception as e:
                        print(f"⚠️ 智能裁剪失败: {e}")

                print(f"✅ 成功: {output_path}")
                return str(output_path)

            except Exception as e:
                print(f"❌ 第 {attempt + 1} 次失败: {e}")
                if attempt == max_retries - 1:
                    raise
                time.sleep(3)

            finally:
                if driver:
                    try:
                        driver.quit()
                    except:
                        pass

    def send_to_telegram(self, image_path, html_filename, bot_token=None, chat_id=None):
        """发送图片到Telegram群组"""
        try:
            # 从参数获取配置，如果没有则从配置文件获取
            if not bot_token or not chat_id:
                try:
                    bot_token = OUTPUT_CONFIG["telegram"]["bot_token"]
                    chat_id = OUTPUT_CONFIG["telegram"]["chat_id"]
                except KeyError:
                    print("⚠️ Telegram配置未设置，跳过发送")
                    return False

            if bot_token == "YOUR_BOT_TOKEN_HERE" or chat_id == "YOUR_CHAT_ID_HERE":
                print("⚠️ Telegram配置未设置，跳过发送")
                return False

            print("📤 发送图片到Telegram群组...")

            # 准备发送的消息
            caption = f"📊 Pythia分析图表\n📅 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n📄 源文件: {html_filename}"

            # 发送图片
            url = f"https://api.telegram.org/bot{bot_token}/sendPhoto"

            with open(image_path, 'rb') as photo:
                files = {'photo': photo}
                data = {
                    'chat_id': chat_id,
                    'caption': caption,
                    'parse_mode': 'HTML'
                }

                response = requests.post(url, files=files, data=data, timeout=30)

            if response.status_code == 200:
                print("✅ 图片已成功发送到Telegram群组")
                return True
            else:
                print(f"❌ Telegram发送失败: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            print(f"❌ Telegram发送异常: {e}")
            return False

    def check_cooldown(self):
        """检查是否在冷却时间内"""
        if self.last_process_time is None:
            return False

        cooldown_minutes = 50
        time_since_last = datetime.now() - self.last_process_time
        cooldown_remaining = timedelta(minutes=cooldown_minutes) - time_since_last

        if cooldown_remaining.total_seconds() > 0:
            remaining_minutes = int(cooldown_remaining.total_seconds() / 60)
            remaining_seconds = int(cooldown_remaining.total_seconds() % 60)
            print(f"❄️ 冷却中... 还需等待 {remaining_minutes}分{remaining_seconds}秒")
            return True

        return False

    def find_latest_html(self):
        """查找最新的HTML文件"""
        data_dir = Path("data")
        if not data_dir.exists():
            print("❌ data目录不存在")
            return None

        html_files = list(data_dir.glob("*.html"))
        if not html_files:
            print("❌ 没有找到HTML文件")
            return None

        latest_file = max(html_files, key=lambda f: f.stat().st_mtime)
        mod_time = datetime.fromtimestamp(latest_file.stat().st_mtime)

        print(f"📁 最新: {latest_file.name}")
        print(f"📅 时间: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")

        return latest_file

class PythiaIntegratedAnalyzer:
    """PYTHIA代币全面分析与报告发送器 - 集成版本"""

    def __init__(self):
        # 从配置文件加载设置
        self.base_url = API_CONFIG["base_url"]
        self.chain_id = PYTHIA_TOKEN_INFO["chain"]
        self.pythia_token_address = PYTHIA_TOKEN_INFO["contract_address"]
        self.timeout = API_CONFIG.get("timeout", 30)  # 增加超时时间到30秒
        self.retry_attempts = API_CONFIG.get("retry_attempts", 5)  # 增加重试次数
        self.retry_delay = API_CONFIG.get("retry_delay", 3)  # 增加重试间隔

        # API速率限制 (每分钟)
        self.rate_limits = API_CONFIG["rate_limits"]

        # 历史数据存储
        self.price_history = []
        self.volume_history = []
        self.last_update = None

        # 监控状态
        self.monitoring = False
        self.monitor_thread = None

        # 初始化HTML转图片转换器
        self.html_converter = HeadlessHTMLConverter()

        # Telegram配置
        try:
            self.telegram_bot_token = OUTPUT_CONFIG["telegram"]["bot_token"]
            self.telegram_chat_id = OUTPUT_CONFIG["telegram"]["chat_id"]
        except KeyError as e:
            print(f"⚠️ 配置文件中缺少Telegram配置项: {e}")
            self.telegram_bot_token = None
            self.telegram_chat_id = None

    def _make_request(self, endpoint: str, params: Dict = None) -> Optional[Dict]:
        """发送API请求 - 带重试机制和更强的错误处理"""
        import ssl
        from urllib3.exceptions import SSLError
        
        for attempt in range(self.retry_attempts):
            try:
                url = f"{self.base_url}{endpoint}"
                
                # 创建会话以复用连接
                session = requests.Session()
                session.headers.update({
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Accept': 'application/json',
                    'Connection': 'keep-alive'
                })
                
                response = session.get(
                    url, 
                    params=params, 
                    timeout=self.timeout,
                    verify=True  # 启用SSL验证
                )

                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 429:  # 速率限制
                    wait_time = self.retry_delay * (attempt + 1)  # 递增等待时间
                    print(f"⚠️ 触发速率限制，等待{wait_time}秒...")
                    time.sleep(wait_time)
                    continue
                else:
                    print(f"❌ API请求失败: {response.status_code} - {response.text}")

            except (requests.exceptions.Timeout, requests.exceptions.ReadTimeout) as e:
                wait_time = self.retry_delay * (attempt + 1)
                print(f"❌ 请求超时 (尝试 {attempt + 1}/{self.retry_attempts}): {e}")
                if attempt < self.retry_attempts - 1:
                    print(f"⏳ 等待{wait_time}秒后重试...")
                    time.sleep(wait_time)
                    
            except (requests.exceptions.SSLError, SSLError, ssl.SSLError) as e:
                wait_time = self.retry_delay * (attempt + 1)
                print(f"❌ SSL连接错误 (尝试 {attempt + 1}/{self.retry_attempts}): {e}")
                if attempt < self.retry_attempts - 1:
                    print(f"⏳ 等待{wait_time}秒后重试...")
                    time.sleep(wait_time)
                    
            except requests.exceptions.ConnectionError as e:
                wait_time = self.retry_delay * (attempt + 1)
                print(f"❌ 连接错误 (尝试 {attempt + 1}/{self.retry_attempts}): {e}")
                if attempt < self.retry_attempts - 1:
                    print(f"⏳ 等待{wait_time}秒后重试...")
                    time.sleep(wait_time)
                    
            except Exception as e:
                wait_time = self.retry_delay * (attempt + 1)
                print(f"❌ 未知错误 (尝试 {attempt + 1}/{self.retry_attempts}): {e}")
                if attempt < self.retry_attempts - 1:
                    print(f"⏳ 等待{wait_time}秒后重试...")
                    time.sleep(wait_time)

        print(f"❌ 所有重试尝试失败，跳过此请求")
        return None

    def search_pythia_pairs(self, query: str = "PYTHIA") -> Optional[Dict]:
        """搜索PYTHIA相关的交易对"""
        endpoint = "/latest/dex/search"
        params = {"q": query}

        print(f"🔍 搜索PYTHIA相关交易对...")
        return self._make_request(endpoint, params)

    def get_token_pairs(self, token_address: str = None) -> Optional[Dict]:
        """获取指定代币的所有交易对"""
        if not token_address:
            token_address = self.pythia_token_address

        endpoint = f"/latest/dex/tokens/{self.chain_id}/{token_address}"

        print(f"📊 获取代币交易对数据...")
        return self._make_request(endpoint)

    def get_specific_pair(self, pair_address: str) -> Optional[Dict]:
        """获取特定交易对的详细信息"""
        endpoint = f"/latest/dex/pairs/{self.chain_id}/{pair_address}"

        print(f"🎯 获取交易对详细信息...")
        return self._make_request(endpoint)

    def analyze_price_data(self, pair_data: Dict) -> Dict[str, Any]:
        """分析价格数据"""
        if not pair_data or "pairs" not in pair_data:
            return {}

        analysis = {}

        for pair in pair_data["pairs"]:
            pair_address = pair.get("pairAddress", "unknown")

            # 基础信息
            analysis[pair_address] = {
                "dex": pair.get("dexId", "unknown"),
                "base_token": pair.get("baseToken", {}).get("symbol", "unknown"),
                "quote_token": pair.get("quoteToken", {}).get("symbol", "unknown"),
                "price_usd": float(pair.get("priceUsd", 0)),
                "price_native": pair.get("priceNative", "0"),
                "market_cap": pair.get("marketCap", 0),
                "fdv": pair.get("fdv", 0),
                "liquidity_usd": pair.get("liquidity", {}).get("usd", 0),
                "volume_24h": pair.get("volume", {}).get("h24", 0),
                "price_change_24h": pair.get("priceChange", {}).get("h24", 0),
                "txns_24h": pair.get("txns", {}).get("h24", {}),
                "pair_created_at": pair.get("pairCreatedAt", 0)
            }

        return analysis

    def analyze_market_sentiment(self, pairs_data: List[Dict]) -> Dict[str, Any]:
        """分析市场情绪"""
        if not pairs_data:
            return {"sentiment": "unknown", "score": 0}

        total_volume = 0
        weighted_price_change = 0
        total_buys = 0
        total_sells = 0

        for pair in pairs_data:
            volume = pair.get("volume", {}).get("h24", 0)
            price_change = pair.get("priceChange", {}).get("h24", 0)
            txns = pair.get("txns", {}).get("h24", {})

            total_volume += volume
            weighted_price_change += price_change * volume
            total_buys += txns.get("buys", 0)
            total_sells += txns.get("sells", 0)

        # 计算加权平均价格变化
        avg_price_change = weighted_price_change / total_volume if total_volume > 0 else 0

        # 计算买卖比例
        buy_sell_ratio = total_buys / total_sells if total_sells > 0 else float('inf')

        # 情绪评分 (0-100)
        sentiment_score = 50  # 基础分数
        sentiment_score += avg_price_change * 2  # 价格变化影响
        sentiment_score += min((buy_sell_ratio - 1) * 10, 20)  # 买卖比例影响
        sentiment_score = max(0, min(100, sentiment_score))  # 限制在0-100

        # 情绪分类
        if sentiment_score >= 80:
            sentiment = "🚀 极度乐观"
        elif sentiment_score >= 65:
            sentiment = "📈 乐观"
        elif sentiment_score >= 50:
            sentiment = "😐 中性"
        elif sentiment_score >= 35:
            sentiment = "📉 悲观"
        else:
            sentiment = "🔻 极度悲观"

        return {
            "sentiment": sentiment,
            "score": sentiment_score,
            "avg_price_change": avg_price_change,
            "buy_sell_ratio": buy_sell_ratio,
            "total_volume": total_volume
        }

    def filter_pairs_by_criteria(self, pairs: List[Dict]) -> tuple[List[Dict], Dict]:
        """根据配置的标准过滤交易对"""
        filter_config = ANALYSIS_CONFIG.get("data_filter_settings", {})

        min_market_cap = filter_config.get("min_market_cap", 50000)
        min_liquidity = filter_config.get("min_liquidity", 10000)
        min_volume_24h = filter_config.get("min_volume_24h", 1000)
        min_price = filter_config.get("min_price", 0.01)
        max_price = filter_config.get("max_price", 1000)

        filtered_pairs = []
        filter_stats = {
            "total_pairs": len(pairs),
            "filtered_by_market_cap": 0,
            "filtered_by_liquidity": 0,
            "filtered_by_volume": 0,
            "filtered_by_price": 0,
            "final_pairs": 0
        }

        for pair in pairs:
            # 获取关键数据
            market_cap = pair.get("marketCap", 0)
            liquidity = pair.get("liquidity", {}).get("usd", 0)
            volume_24h = pair.get("volume", {}).get("h24", 0)
            price = float(pair.get("priceUsd", 0))

            # 应用过滤条件
            if market_cap < min_market_cap:
                filter_stats["filtered_by_market_cap"] += 1
                continue

            if liquidity < min_liquidity:
                filter_stats["filtered_by_liquidity"] += 1
                continue

            if volume_24h < min_volume_24h:
                filter_stats["filtered_by_volume"] += 1
                continue

            if price < min_price or price > max_price:
                filter_stats["filtered_by_price"] += 1
                continue

            # 通过所有过滤条件
            filtered_pairs.append(pair)

        filter_stats["final_pairs"] = len(filtered_pairs)
        return filtered_pairs, filter_stats

    def search_all_pythia_pairs(self) -> tuple[List[Dict], Dict]:
        """搜索所有PYTHIA相关交易对并应用过滤 - 带智能重试"""
        all_pairs = []
        search_keywords = SEARCH_KEYWORDS
        successful_searches = 0
        total_searches = len(search_keywords)

        for keyword in search_keywords:
            print(f"🔍 搜索关键词: {keyword}")
            
            # 尝试搜索，如果失败则跳过但不中断整个流程
            results = self.search_pythia_pairs(keyword)

            if results and "pairs" in results:
                pairs = results["pairs"]
                print(f"   找到 {len(pairs)} 个交易对")
                all_pairs.extend(pairs)
                successful_searches += 1
            else:
                print(f"   ⚠️ 搜索失败，跳过关键词: {keyword}")
                
            # 在搜索间隔中等待，避免速率限制
            time.sleep(1.0)

        print(f"📈 搜索完成: {successful_searches}/{total_searches} 个关键词成功")

        # 如果没有任何成功的搜索，尝试备用策略
        if not all_pairs:
            print("🔄 启用备用搜索策略...")
            backup_result = self.search_pythia_pairs("PYTHIA")  # 只搜索主要关键词
            if backup_result and "pairs" in backup_result:
                all_pairs.extend(backup_result["pairs"])
                print(f"   备用搜索找到 {len(backup_result['pairs'])} 个交易对")

        # 去重
        unique_pairs = {}
        for pair in all_pairs:
            pair_addr = pair.get("pairAddress")
            if pair_addr and pair_addr not in unique_pairs:
                unique_pairs[pair_addr] = pair

        raw_pairs = list(unique_pairs.values())

        # 应用过滤条件
        filtered_pairs, filter_stats = self.filter_pairs_by_criteria(raw_pairs)

        print(f"📊 数据过滤结果:")
        print(f"   原始交易对: {filter_stats['total_pairs']}")
        print(f"   过滤后交易对: {filter_stats['final_pairs']}")

        return filtered_pairs, filter_stats

    def calculate_comprehensive_metrics(self, pairs_data: List[Dict], analysis: Dict) -> Dict:
        """计算综合市场指标"""
        metrics = {
            'total_pairs': len(pairs_data),
            'total_volume': 0,
            'total_liquidity': 0,
            'total_market_cap': 0,
            'total_buys': 0,
            'total_sells': 0,
            'price_range': {'min': float('inf'), 'max': 0},
            'dex_distribution': {},
            'positive_pairs': 0,
            'negative_pairs': 0,
            'neutral_pairs': 0,
            'highest_volume_pair': None,
            'highest_volume': 0
        }

        for pair in pairs_data:
            # 交易量和流动性
            volume = pair.get('volume', {}).get('h24', 0)
            liquidity = pair.get('liquidity', {}).get('usd', 0)
            market_cap = pair.get('marketCap', 0)
            price = float(pair.get('priceUsd', 0))
            price_change = pair.get('priceChange', {}).get('h24', 0)

            metrics['total_volume'] += volume
            metrics['total_liquidity'] += liquidity
            
            # 找到交易量最高的交易对，使用其市值作为标准
            if volume > metrics['highest_volume']:
                metrics['highest_volume'] = volume
                metrics['highest_volume_pair'] = pair
                if market_cap > 0:
                    metrics['total_market_cap'] = market_cap

            # 价格范围 - 过滤异常价格数据
            if price > 0.01 and price < 1000:
                metrics['price_range']['min'] = min(metrics['price_range']['min'], price)
                metrics['price_range']['max'] = max(metrics['price_range']['max'], price)

            # 买卖统计
            txns = pair.get('txns', {}).get('h24', {})
            metrics['total_buys'] += txns.get('buys', 0)
            metrics['total_sells'] += txns.get('sells', 0)

            # DEX分布
            dex = pair.get('dexId', 'unknown')
            metrics['dex_distribution'][dex] = metrics['dex_distribution'].get(dex, 0) + 1

            # 价格变化分布
            if price_change > 1:
                metrics['positive_pairs'] += 1
            elif price_change < -1:
                metrics['negative_pairs'] += 1
            else:
                metrics['neutral_pairs'] += 1

        return metrics

    def generate_fallback_report(self) -> str:
        """生成备用报告 - 当网络请求失败时使用"""
        now = datetime.now()
        
        fallback_html = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=1280, initial-scale=1.0">
    <title>PYTHIA 分析报告 - 网络异常</title>
    <style>
        body {{
            font-family: 'Arial', sans-serif;
            background: #0D1117;
            color: #E6EDF3;
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }}
        .error-container {{
            background: #161B22;
            border: 1px solid #30363D;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            max-width: 600px;
        }}
        .error-icon {{
            font-size: 4rem;
            margin-bottom: 20px;
        }}
        .error-title {{
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #F59E0B;
        }}
        .error-message {{
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: #B0BAC6;
        }}
        .retry-info {{
            background: #0D1117;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            font-size: 0.9rem;
            color: #7D8590;
        }}
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">🔌</div>
        <h1 class="error-title">网络连接异常</h1>
        <p class="error-message">
            无法连接到DexScreener API服务器。<br>
            这可能是由于网络连接问题或API服务暂时不可用。
        </p>
        <div class="retry-info">
            <strong>生成时间:</strong> {now.strftime('%Y-%m-%d %H:%M:%S')}<br>
            <strong>状态:</strong> 自动重试机制已启用<br>
            <strong>建议:</strong> 系统将在下个周期自动重试
        </div>
    </div>
</body>
</html>"""
        
        return fallback_html

    def generate_professional_report(self) -> str:
        """生成专业级PYTHIA代币分析报告 - HTML格式"""
        print("🐭 开始生成PYTHIA专业分析报告...")
        print("=" * 60)

        # 获取并过滤数据
        pairs_data, filter_stats = self.search_all_pythia_pairs()
        if not pairs_data:
            print("⚠️ 无法获取实时数据，尝试生成基础报告...")
            return self.generate_fallback_report()

        formatted_data = {"pairs": pairs_data}
        analysis = self.analyze_price_data(formatted_data)
        sentiment = self.analyze_market_sentiment(pairs_data)
        metrics = self.calculate_comprehensive_metrics(pairs_data, analysis)

        # 开始生成报告
        now = datetime.now()

        # 计算关键指标
        total_market_cap = metrics['total_market_cap']
        total_volume = metrics['total_volume']
        total_liquidity = metrics['total_liquidity']
        total_buys = metrics['total_buys']
        total_sells = metrics['total_sells']
        buy_sell_ratio = total_buys / total_sells if total_sells > 0 else 0
        
        # 价格一致性分析
        min_price = metrics['price_range']['min'] if metrics['price_range']['min'] != float('inf') else 0
        max_price = metrics['price_range']['max']
        if min_price > 0 and max_price > 0:
            price_diff = ((max_price - min_price) / min_price * 100)
        else:
            price_diff = 0

        # 技术指标
        avg_price_change = sum([data['price_change_24h'] for data in analysis.values()]) / len(analysis) if analysis else 0
        volume_to_mcap = (total_volume / total_market_cap * 100) if total_market_cap > 0 else 0

        # 生成积极因素HTML
        positive_factors = []
        if buy_sell_ratio > 1.2:
            positive_factors.append(f"强劲的买入压力: 买卖比例{buy_sell_ratio:.2f}:1")
        if total_volume > 1000000:
            positive_factors.append(f"高交易活跃度: 日交易量>${total_volume/1000000:.0f}M")
        if len(metrics['dex_distribution']) >= 3:
            positive_factors.append(f"多DEX支持: 分布在{len(metrics['dex_distribution'])}个DEX")
        
        factors_html = "".join([f"<li>{factor}</li>" for factor in positive_factors])



        # 生成交易对表格HTML - 显示所有交易对
        pairs_table_html = ""
        if analysis:
            sorted_pairs = sorted(analysis.items(), key=lambda x: x[1].get('volume_24h', 0), reverse=True)
            for pair_addr, data in sorted_pairs:  # 移除[:10]限制，显示所有交易对
                change_color = "#28A745" if data['price_change_24h'] > 0 else "#DC3545"
                pairs_table_html += f"""
                <tr>
                    <td>{data['base_token']}/{data['quote_token']}<br><small style="color: var(--text-muted);">{data['dex']}</small></td>
                    <td>${data['price_usd']:.6f}</td>
                    <td style="color: {change_color};">{data['price_change_24h']:+.2f}%</td>
                    <td>${data['volume_24h']/1000000:.2f}M</td>
                </tr>"""

        # DEX分布模块已删除

        # 生成流动性分布HTML - 采用表格格式，显示所有有流动性的交易对
        liquidity_html = ""
        liquidity_count = 0
        if analysis:
            sorted_by_liquidity = sorted(analysis.items(), key=lambda x: x[1].get('liquidity_usd', 0), reverse=True)
            for i, (pair_addr, data) in enumerate(sorted_by_liquidity, 1):
                # 只显示流动性大于10美元的交易对（放宽条件）
                if data['liquidity_usd'] > 10:
                    percentage = (data['liquidity_usd'] / total_liquidity * 100) if total_liquidity > 0 else 0
                    liquidity_html += f"""
                    <tr>
                        <td>{data['base_token']}/{data['quote_token']}<br><small style="color: var(--text-muted);">{data['dex']}</small></td>
                        <td>${data['liquidity_usd']/1000000:.2f}M</td>
                        <td>{percentage:.1f}%</td>
                    </tr>"""
                    liquidity_count += 1

        # 生成HTML报告
        report = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=1280, initial-scale=1.0">
    <title>PYTHIA 分析报告 (最终复刻版)</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&family=JetBrains+Mono:wght@400;700&display=swap" rel="stylesheet">
    <style>
        :root {{
            --bg-primary: #0D1117;
            --bg-secondary: #101419;
            --bg-module: #161B22;
            --text-primary: #E6EDF3;
            --text-secondary: #B0BAC6;
            --text-muted: #7D8590;
            --accent-primary: #F2BC8C;
            --border-primary: #30363D;
            --border-secondary: #21262D;
            --success: #28A745;
            --danger: #F59E0B;
            
            /* 字体大小变量 - 弹性计算 */
            --base-font-size: 1rem;
            --small-font-size: 0.85rem;
            --medium-font-size: 1.1rem;
            --large-font-size: 1.3rem;
            --xlarge-font-size: 1.6rem;
            --xxlarge-font-size: 2rem;
            
            /* 间距变量 */
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 0.75rem;
            --spacing-lg: 1rem;
            --spacing-xl: 1.5rem;
        }}
        *, *::before, *::after {{
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }}
        body, html {{
            font-family: 'Noto Sans SC', sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: var(--base-font-size);
            line-height: 1.4;
            -webkit-font-smoothing: antialiased;
            overflow: hidden;
            margin: 0;
            padding: 0;
            width: 100vw;
            height: 100vh;
        }}
        .report-container {{
            width: 1280px;
            height: 720px;
            background: var(--bg-primary);
            display: flex;
            flex-direction: column;
            margin: 0 auto;
            position: relative;
        }}
        .report-header {{
            padding: 8px 20px;
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--border-primary);
            flex-shrink: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 50px;
        }}
        .header-title h1 {{
            font-family: 'Noto Sans SC', sans-serif;
            font-size: var(--xlarge-font-size);
            font-weight: 700;
        }}
        .header-title h1 .logo {{
            font-family: 'JetBrains Mono', monospace;
            margin-right: 6px;
            color: var(--accent-primary);
        }}
        .header-meta {{
            background-color: #0D1117;
            border-radius: 12px;
            padding: var(--spacing-xs) var(--spacing-md);
            font-size: var(--small-font-size);
            color: var(--text-muted);
            font-family: 'JetBrains Mono', monospace;
        }}
        .header-meta strong {{
            color: var(--text-secondary);
        }}
        .report-main {{
            flex-grow: 1;
            padding: 10px;
            display: flex;
            gap: 10px;
            overflow: hidden;
            height: calc(720px - 50px - 25px);
        }}
        .left-column {{
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
            height: 100%;
        }}
        .center-column {{
            flex: 2;
            display: flex;
            flex-direction: column;
            gap: 8px;
            height: 100%;
        }}
        .right-column {{
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
            height: 100%;
        }}
        .column {{
            display: flex;
            flex-direction: column;
            gap: 12px;
            min-height: 0;
            overflow: hidden;
        }}
        .module-card {{
            background: var(--bg-module);
            border-radius: 6px;
            border: 1px solid var(--border-primary);
            padding: 12px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }}
        .module-card .module-title {{
            font-size: var(--medium-font-size);
            font-weight: 500;
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            flex-shrink: 0;
        }}
        .module-card.stretchy {{
            flex-grow: 1;
            min-height: 0;
        }}
        .analysis-list {{
            list-style: none;
            font-size: var(--base-font-size);
            padding-left: var(--spacing-xs);
            overflow: hidden;
            flex-grow: 1;
        }}
        .analysis-list li {{
            margin-bottom: var(--spacing-sm);
            line-height: 1.3;
        }}
        .enhanced-metrics-table {{
            width: 100%;
            border-collapse: collapse;
            flex-grow: 1;
        }}
        .enhanced-metrics-table td {{
            padding: var(--spacing-sm) var(--spacing-xs);
            border-bottom: 1px solid var(--border-secondary);
            font-size: var(--base-font-size);
            vertical-align: middle;
        }}
        .enhanced-metrics-table tr:last-child td {{
            border-bottom: none;
            padding-bottom: 0;
        }}
        .enhanced-metrics-table tr:first-child td {{
            padding-top: 0;
        }}
        .enhanced-metrics-table .metric-label {{
            color: var(--text-muted);
            line-height: 1.2;
        }}
        .enhanced-metrics-table .metric-value {{
            font-weight: 500;
            text-align: right;
            font-family: 'JetBrains Mono', monospace;
            color: var(--text-primary);
        }}
        .core-metrics-grid {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 0;
        }}
        .metric-item {{
            background-color: var(--bg-secondary);
            border-radius: 4px;
            padding: 8px;
        }}
        .metric-item .label {{
            font-size: var(--small-font-size);
            margin-bottom: var(--spacing-xs);
            color: var(--text-muted);
        }}
        .metric-item .value {{
            font-size: var(--large-font-size);
            font-weight: 700;
            font-family: 'JetBrains Mono', monospace;
        }}
        .chart-module {{
            flex-grow: 1;
            padding: 12px;
            min-height: 0;
        }}
        .chart-container {{
            flex-grow: 1;
            min-height: 0;
            height: 100%;
        }}
        #chart_container {{
            width: 100%;
            height: 100%;
        }}
        .table-module {{
            flex-grow: 1;
            overflow: hidden;
            min-height: 0;
        }}
        .table-container {{
            height: 100%;
            overflow: hidden;
            flex-grow: 1;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            font-size: var(--base-font-size);
        }}
        th, td {{
            padding: var(--spacing-sm) var(--spacing-xs);
            text-align: left;
            border-bottom: 1px solid var(--border-secondary);
        }}
        th {{
            font-size: var(--small-font-size);
            text-transform: uppercase;
            color: var(--text-muted);
            position: sticky;
            top: 0;
            background: var(--bg-module);
            padding-bottom: var(--spacing-sm);
        }}
        
        /* 交易对详情表格 - 固定字体大小，完美填充 */
        .table-module table {{
            font-size: 11px;
            height: 100%;
            width: 100%;
        }}
        .table-module th {{
            font-size: 10px;
            padding: 4px 3px;
            padding-bottom: 6px;
            line-height: 1.1;
            font-weight: 600;
        }}
        .table-module td {{
            padding: 4px 3px;
            font-size: 11px;
            line-height: 1.2;
            vertical-align: top;
        }}
        .table-module small {{
            font-size: 9px;
            line-height: 1.1;
            opacity: 0.8;
        }}
        
        /* 流动性分布表格 - 与交易对详情表格统一字体大小 */
        .liquidity-table table {{
            font-size: 11px !important;
            height: 100% !important;
        }}
        .liquidity-table th {{
            font-size: 10px !important;
            padding: 4px 3px !important;
            padding-bottom: 6px !important;
            line-height: 1.1 !important;
            font-weight: 600 !important;
        }}
        .liquidity-table td {{
            padding: 4px 3px !important;
            font-size: 11px !important;
            line-height: 1.2 !important;
            vertical-align: top !important;
        }}
        .liquidity-table small {{
            font-size: 9px !important;
            line-height: 1.1 !important;
            opacity: 0.8 !important;
        }}
        
        /* 确保流动性表格充分利用垂直空间 - 强制填充 */
        .liquidity-table .table-container {{
            height: 100% !important;
            display: flex !important;
            flex-direction: column !important;
        }}
        .liquidity-table table {{
            height: 100% !important;
            flex-grow: 1 !important;
            display: table !important;
        }}
        .liquidity-table tbody {{
            height: 100% !important;
            display: table-row-group !important;
        }}
        .liquidity-table tbody tr {{
            height: 20% !important;
            display: table-row !important;
        }}
        .liquidity-table thead {{
            display: table-header-group !important;
        }}
        .liquidity-table thead tr {{
            height: 20% !important;
        }}
        
        /* 确保表格容器无滚动条，完美填充 */
        .table-container {{
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }}
        .table-container table {{
            flex-grow: 1;
            height: 100%;
            table-layout: fixed;
        }}
        
        /* 表格列宽优化 */
        .table-module th:nth-child(1),
        .table-module td:nth-child(1) {{
            width: 35%;
        }}
        .table-module th:nth-child(2),
        .table-module td:nth-child(2) {{
            width: 25%;
        }}
        .table-module th:nth-child(3),
        .table-module td:nth-child(3) {{
            width: 20%;
        }}
        .table-module th:nth-child(4),
        .table-module td:nth-child(4) {{
            width: 20%;
        }}
        tr:last-child td {{
            border-bottom: none;
        }}
        .dex-distribution {{
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            overflow: hidden;
            height: 100%;
        }}
        .dex-tag {{
            background-color: var(--bg-secondary);
            color: var(--text-secondary);
            font-size: var(--small-font-size);
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: 3px;
            white-space: nowrap;
        }}
        #liquidity-container {{
            font-size: var(--base-font-size);
            overflow-y: auto;
            max-height: 100px;
        }}
        .chart-tabs {{
            display: flex;
            margin-bottom: 10px;
            border-bottom: 1px solid var(--border-primary);
        }}
        .tab-btn {{
            background: none;
            border: none;
            color: var(--text-muted);
            padding: var(--spacing-sm) var(--spacing-md);
            font-size: var(--base-font-size);
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
        }}
        .tab-btn:hover {{
            color: var(--text-secondary);
        }}
        .tab-btn.active {{
            color: var(--accent-primary);
            border-bottom-color: var(--accent-primary);
        }}
        .chart-content {{
            display: none;
            height: 100%;
            width: 100%;
        }}
        .chart-content.active {{
            display: block;
        }}
        #tradingview_chart {{
            width: 100%;
            height: 100%;
        }}
        canvas {{
            width: 100% !important;
            height: 100% !important;
        }}
        .footer {{
            padding: var(--spacing-sm);
            border-top: 1px solid var(--border-primary);
            font-size: var(--small-font-size);
            color: var(--text-muted);
            text-align: center;
            flex-shrink: 0;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
        }}
    </style>
</head>
<body>
    <div class="report-container">
        <header class="report-header">
            <div class="header-title">
                <h1><span class="logo">🐭 PYTHIA</span>分析报告</h1>
            </div>
            <div class="header-meta">
                <span><strong>Token:</strong> {self.pythia_token_address[:12]}...</span> |
                <span><strong>Chain:</strong> Solana</span> |
                <span><strong>Time:</strong> {now.strftime('%Y-%m-%d %H:%M')}</span>
            </div>
        </header>
        <main class="report-main">
            <!-- LEFT COLUMN -->
            <div class="left-column">
                <div class="module-card">
                    <h2 class="module-title">✅ 积极因素</h2>
                    <ul class="analysis-list">{factors_html}</ul>
                </div>
                <div class="module-card stretchy">
                    <h2 class="module-title">🔬 增强指标</h2>
                    <table class="enhanced-metrics-table">
                        <tbody>
                            <tr>
                                <td class="metric-label">平均价格变化</td>
                                <td class="metric-value">{avg_price_change:.2f}%</td>
                            </tr>
                            <tr>
                                <td class="metric-label">价格一致性</td>
                                <td class="metric-value">{price_diff:.2f}%</td>
                            </tr>
                            <tr>
                                <td class="metric-label">交易活跃度</td>
                                <td class="metric-value">{(total_buys + total_sells) / len(pairs_data) if pairs_data else 0:.0f} 笔/对</td>
                            </tr>
                            <tr>
                                <td class="metric-label">市场深度</td>
                                <td class="metric-value">{(total_liquidity / total_market_cap) if total_market_cap > 0 else 0:.3f}</td>
                            </tr>
                            <tr>
                                <td class="metric-label">情绪指数</td>
                                <td class="metric-value">{sentiment['sentiment']}</td>
                            </tr>
                            <tr>
                                <td class="metric-label">买卖比</td>
                                <td class="metric-value">{buy_sell_ratio:.2f}:1</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- CENTER COLUMN -->
            <div class="center-column">
                <div class="module-card">
                    <div class="core-metrics-grid">
                        <div class="metric-item">
                            <div class="label">市值</div>
                            <div class="value">${total_market_cap/1000000:.2f}M</div>
                        </div>
                        <div class="metric-item">
                            <div class="label">24h 交易量</div>
                            <div class="value">${total_volume/1000000:.2f}M</div>
                        </div>
                        <div class="metric-item">
                            <div class="label">流动性</div>
                            <div class="value">${total_liquidity/1000000:.2f}M</div>
                        </div>
                        <div class="metric-item">
                            <div class="label">换手率</div>
                            <div class="value">{volume_to_mcap:.1f}%</div>
                        </div>
                    </div>
                </div>
                <div class="module-card stretchy chart-module">
                    <h2 class="module-title">📈 价格走势图 (24H)</h2>
                    <div class="chart-container">
                        <div id="chart_container"></div>
                    </div>
                </div>
            </div>
            <!-- RIGHT COLUMN -->
            <div class="right-column">
                <div class="module-card table-module" style="flex: 1; min-height: 0;">
                    <h2 class="module-title">💰 交易对详情</h2>
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>交易对 / DEX</th>
                                    <th>价格</th>
                                    <th>24h%</th>
                                    <th>交易量</th>
                                </tr>
                            </thead>
                            <tbody>{pairs_table_html}</tbody>
                        </table>
                    </div>
                </div>
                <div class="module-card table-module liquidity-table" style="flex: 1; min-height: 0;">
                    <h2 class="module-title">💧 流动性分布</h2>
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>交易对 / DEX</th>
                                    <th>流动性</th>
                                    <th>占比</th>
                                </tr>
                            </thead>
                            <tbody>{liquidity_html}</tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
        <footer class="footer">
            PYTHIA AI 增强分析工具 v3.0 | "数据驱动决策，理性投资未来"
        </footer>
    </div>
    <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
    <script type="text/javascript">
        window.addEventListener('load', function () {{
            new TradingView.widget({{
                "autosize": true,
                "symbol": "CRYPTO:PYTHIAUSD",
                "interval": "30",
                "theme": "dark",
                "style": "1",
                "locale": "zh_CN",
                "toolbar_bg": "#161B22",
                "enable_publishing": false,
                "hide_top_toolbar": true,
                "hide_side_toolbar": true,
                "hide_legend": true,
                "container_id": "chart_container",
                "overrides": {{
                    "paneProperties.background": "#161B22",
                    "paneProperties.vertGridProperties.color": "rgba(255,255,255,0.05)",
                    "paneProperties.horzGridProperties.color": "rgba(255,255,255,0.05)",
                    "mainSeriesProperties.candleStyle.upColor": "#28A745",
                    "mainSeriesProperties.candleStyle.downColor": "#DC3545",
                    "mainSeriesProperties.candleStyle.borderUpColor": "#28A745",
                    "mainSeriesProperties.candleStyle.borderDownColor": "#DC3545",
                    "scalesProperties.textColor": "#7D8590"
                }}
            }});
        }});
    </script>
</body>
</html>"""

        return report

    def save_report_to_file(self, report_content: str) -> str:
        """保存报告到文件，并自动转换为图片发送到Telegram"""
        try:
            # 确保data目录存在
            data_dir = OUTPUT_CONFIG.get("data_directory", "data")
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)

            # 生成文件名 - 现在保存为HTML格式
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"pythia_analysis_{timestamp}.html"
            filepath = os.path.join(data_dir, filename)

            # 保存HTML文件
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(report_content)

            print(f"✅ HTML报告已保存: {filepath}")

            # 自动转换HTML为图片
            try:
                print("🔄 开始将HTML转换为图片...")
                image_path = self.html_converter.convert_html_to_image(filepath)

                if image_path:
                    print(f"✅ 图片转换成功: {image_path}")

                    # 发送图片到Telegram群组
                    if self.telegram_bot_token and self.telegram_chat_id:
                        success = self.html_converter.send_to_telegram(
                            image_path, filename, self.telegram_bot_token, self.telegram_chat_id
                        )
                        if success:
                            print("✅ 图片已成功发送到Telegram群组")
                        else:
                            print("❌ Telegram发送失败")
                    else:
                        print("⚠️ Telegram配置未设置，跳过发送")
                else:
                    print("❌ 图片转换失败")

            except Exception as e:
                print(f"❌ HTML转图片过程出错: {e}")

            return filepath

        except Exception as e:
            print(f"❌ 保存报告失败: {e}")
            return ""

    def auto_generate_report(self, quiet_mode: bool = False, auto_send: bool = False) -> str:
        """自动生成报告并保存"""
        try:
            if not quiet_mode:
                print("🐭 开始自动生成PYTHIA分析报告...")

            # 生成报告
            report_content = self.generate_professional_report()
            
            if not report_content or report_content.startswith("❌"):
                if not quiet_mode:
                    print("❌ 报告生成失败")
                return ""

            # 保存报告
            filepath = self.save_report_to_file(report_content)
            
            if not filepath:
                if not quiet_mode:
                    print("❌ 报告保存失败")
                return ""

            # 只有在明确要求自动发送时才发送
            if auto_send:
                send_config = getattr(config, 'REPORT_SEND_CONFIG', {})
                if send_config.get('send_on_generation', False):
                    if not quiet_mode:
                        print("� 报在告已保存，跳过发送到Telegram")
                    # 注释掉Telegram发送 - 只存储不发送
                    # success = asyncio.run(self.telegram_sender.send_to_subscribers(filepath))
                    success = True  # 假设成功，因为我们只存储
                    if success and not quiet_mode:
                        print("✅ 报告存储成功")
                    elif not quiet_mode:
                        print("❌ 报告发送失败")

            return filepath

        except Exception as e:
            if not quiet_mode:
                print(f"❌ 自动生成报告出错: {e}")
            return ""

    def quick_analysis(self):
        """快速分析PYTHIA代币"""
        print("🐭 PYTHIA代币快速分析")
        print("=" * 40)
        print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()

        try:
            # 搜索并过滤PYTHIA交易对
            print("🔍 正在获取PYTHIA数据...")
            pairs, filter_stats = self.search_all_pythia_pairs()

            if not pairs:
                print("❌ 未找到符合条件的PYTHIA数据")
                return

            print(f"✅ 找到 {len(pairs)} 个符合条件的交易对")

            # 分析数据
            formatted_data = {"pairs": pairs}
            analysis = self.analyze_price_data(formatted_data)

            if not analysis:
                print("❌ 数据分析失败")
                return

            # 找到主要交易对（交易量最大的）
            main_pair = max(analysis.items(), key=lambda x: x[1].get('volume_24h', 0))
            pair_addr, data = main_pair

            print("\n📊 主要交易对数据:")
            print(f"🔗 {data['base_token']}/{data['quote_token']} ({data['dex']})")
            print(f"💰 当前价格: ${data['price_usd']:.8f}")
            print(f"📈 24h变化: {data['price_change_24h']:+.2f}%")
            print(f"💧 流动性: ${data['liquidity_usd']:,.2f}")
            print(f"📊 24h交易量: ${data['volume_24h']:,.2f}")
            print(f"🏪 市值: ${data['market_cap']:,.2f}")

            # 交易统计
            txns = data['txns_24h']
            if isinstance(txns, dict):
                buys = txns.get('buys', 0)
                sells = txns.get('sells', 0)
                ratio = buys / sells if sells > 0 else float('inf')
                print(f"🔄 买卖比例: {buys}/{sells} (比例: {ratio:.2f})")

            # 市场情绪
            if data['price_change_24h'] > 5:
                sentiment = "🚀 强势上涨"
            elif data['price_change_24h'] > 0:
                sentiment = "📈 温和上涨"
            elif data['price_change_24h'] > -5:
                sentiment = "📉 小幅下跌"
            else:
                sentiment = "🔻 大幅下跌"

            print(f"🎯 市场情绪: {sentiment}")

            print(f"\n📍 主要交易对地址: {pair_addr}")

        except Exception as e:
            print(f"❌ 分析过程出错: {e}")

    def continuous_report_generator(self):
        """持续报告生成器"""
        print("🐭 PYTHIA持续报告生成器启动")
        print("=" * 50)
        print(f"🕒 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("⏰ 生成频率: 每60分钟一次")
        print("📱 自动发送: 根据配置决定")
        print()

        # 询问是否立即执行一次
        try:
            choice = input("是否立即执行一次测试？(y/n): ").lower().strip()
            if choice in ['y', 'yes', '是']:
                print("\n🚀 立即执行测试...")
                self.generate_and_send_report()
        except KeyboardInterrupt:
            print("\n❌ 已取消")
            return

        print("\n🔄 进入持续运行模式...")
        print("💡 按 Ctrl+C 停止运行")
        print("=" * 50)

        try:
            while True:
                print(f"\n⏰ {datetime.now().strftime('%H:%M:%S')} - 等待下次执行...")
                time.sleep(3600)  # 等待60分钟
                
                print(f"\n🕒 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 开始生成报告")
                self.generate_and_send_report()
                
        except KeyboardInterrupt:
            print(f"\n\n⏹️ 用户停止运行")
            print("👋 感谢使用PYTHIA持续报告生成器！")

    def generate_report_with_retry(self, max_attempts=3):
        """带重试机制的报告生成"""
        for attempt in range(max_attempts):
            try:
                print(f"🔄 尝试生成报告 (第 {attempt + 1}/{max_attempts} 次)")
                report_html = self.generate_professional_report()
                
                # 检查是否是备用报告
                if "网络连接异常" not in report_html:
                    print("✅ 报告生成成功")
                    return report_html
                else:
                    print("⚠️ 生成了备用报告，尝试重新获取数据...")
                    if attempt < max_attempts - 1:
                        wait_time = (attempt + 1) * 30  # 递增等待时间
                        print(f"⏳ 等待 {wait_time} 秒后重试...")
                        time.sleep(wait_time)
                        
            except Exception as e:
                print(f"❌ 报告生成失败: {e}")
                if attempt < max_attempts - 1:
                    wait_time = (attempt + 1) * 30
                    print(f"⏳ 等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                    
        print("⚠️ 所有重试尝试完成，返回备用报告")
        return self.generate_fallback_report()

    def generate_and_send_report_with_retry(self):
        """生成并存储报告 - 带重试机制，只存储不发送到Telegram"""
        try:
            print(f"\n🕒 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 开始生成报告")
            print("=" * 60)
            
            print("📊 正在生成PYTHIA分析报告...")
            
            # 使用重试机制生成报告
            report_html = self.generate_report_with_retry(max_attempts=5)
            
            if not report_html:
                print("❌ 报告生成完全失败")
                return False
            
            # 保存报告到文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"pythia_analysis_{timestamp}.html"
            filepath = os.path.join(OUTPUT_CONFIG["data_directory"], filename)
            
            # 确保目录存在
            os.makedirs(OUTPUT_CONFIG["data_directory"], exist_ok=True)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(report_html)
            
            print(f"✅ 报告已保存: {filepath}")
            print(f"✅ 报告生成成功: {filename}")
            
            print("💾 报告已保存到本地，跳过Telegram发送")
            print("✅ 报告存储成功")
            print("=" * 60)
            return True
            
        except Exception as e:
            print(f"❌ 生成报告出错: {e}")
            return False

    def generate_and_send_report(self):
        """生成并存储报告 - 只存储不发送到Telegram"""
        try:
            print(f"\n🕒 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 开始生成报告")
            print("=" * 60)
            
            print("📊 正在生成PYTHIA分析报告...")
            # 只生成报告，不自动发送
            filepath = self.auto_generate_report(quiet_mode=True, auto_send=False)

            if not filepath:
                print("❌ 报告生成失败")
                return False
            
            print(f"✅ 报告生成成功: {os.path.basename(filepath)}")
            
            # 注释掉Telegram发送部分 - 只存储不发送
            # print("📤 正在发送到群组...")
            # success = asyncio.run(self.send_to_group(filepath))
            
            print("💾 报告已保存到本地，跳过Telegram发送")
            success = True  # 假设成功，因为我们只存储
            
            if success:
                print("✅ 报告存储成功")
            
            print("=" * 60)
            return success
            
        except Exception as e:
            print(f"❌ 生成报告出错: {e}")
            return False

    async def send_to_group(self, filepath):
        """注释掉发送报告到群组功能 - 只存储不发送"""
        try:
            print("💾 报告已保存到本地，跳过群组发送")
            # 注释掉Telegram发送功能
            # group_chats = getattr(config, 'TELEGRAM_REPORT_CHATS', [])
            # if not group_chats:
            #     print("❌ 未配置群组")
            #     return False
            # 
            # success = await self.telegram_sender.send_report_text(filepath, group_chats)
            # return success
            return True  # 假设成功，因为我们只存储
            
        except Exception as e:
            print(f"❌ 处理报告失败: {e}")
            return False

    def start_auto_scheduler(self):
        """启动自动调度器 - 根据配置文件设置"""
        auto_run_config = ANALYSIS_CONFIG.get("auto_run_settings", {})
        interval_minutes = auto_run_config.get("interval_minutes", 60)
        run_on_startup = auto_run_config.get("run_on_startup", True)
        quiet_mode = auto_run_config.get("quiet_mode", False)
        
        print("🐭 PYTHIA自动报告发送器")
        print("=" * 50)
        print(f"🕒 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏰ 发送间隔: {interval_minutes}分钟")
        print(f"📱 启动时发送: {'是' if run_on_startup else '否'}")
        print(f"🔇 静默模式: {'是' if quiet_mode else '否'}")
        print()
        
        # 设置定时任务 - 使用带重试机制的版本
        schedule.every(interval_minutes).minutes.do(self.generate_and_send_report_with_retry)
        
        # 显示下次执行时间
        next_run = schedule.next_run()
        print(f"⏰ 下次执行时间: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # 启动时立即执行一次
        if run_on_startup:
            print("🚀 启动时立即执行一次...")
            self.generate_and_send_report_with_retry()
            print()
        
        print("🔄 进入定时运行模式...")
        print("💡 按 Ctrl+C 停止运行")
        print("=" * 50)
        
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
                
        except KeyboardInterrupt:
            print(f"\n\n⏹️ 用户停止运行")
            print("👋 感谢使用PYTHIA自动报告发送器！")

    def start_continuous_mode(self):
        """启动持续运行模式（备用方案，不使用schedule库）"""
        auto_run_config = ANALYSIS_CONFIG.get("auto_run_settings", {})
        interval_minutes = auto_run_config.get("interval_minutes", 60)
        run_on_startup = auto_run_config.get("run_on_startup", True)
        
        print("🐭 PYTHIA持续运行模式")
        print("=" * 50)
        print(f"🕒 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏰ 发送间隔: {interval_minutes}分钟")
        print(f"📱 启动时发送: {'是' if run_on_startup else '否'}")
        print()
        
        # 启动时立即执行一次
        if run_on_startup:
            print("🚀 启动时立即执行一次...")
            self.generate_and_send_report()
            print()
        
        print("🔄 进入持续运行模式...")
        print("💡 按 Ctrl+C 停止运行")
        print("=" * 50)
        
        try:
            while True:
                print(f"\n⏰ {datetime.now().strftime('%H:%M:%S')} - 等待{interval_minutes}分钟后执行...")
                time.sleep(interval_minutes * 60)  # 转换为秒
                
                print(f"\n🕒 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 开始生成报告")
                self.generate_and_send_report()
                
        except KeyboardInterrupt:
            print(f"\n\n⏹️ 用户停止运行")
            print("👋 感谢使用PYTHIA持续报告生成器！")

    def start_hourly_scheduler(self):
        """启动每小时定时任务（兼容性方法）"""
        print("🐭 PYTHIA每小时报告发送器")
        print("=" * 50)
        print(f"🕒 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("⏰ 发送频率: 每小时一次")
        print()
        
        # 设置每小时执行一次
        schedule.every().hour.at(":00").do(self.generate_and_send_report)
        
        # 显示下次执行时间
        next_run = schedule.next_run()
        print(f"⏰ 下次执行时间: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # 询问是否立即执行一次
        try:
            choice = input("是否立即执行一次测试？(y/n): ").lower().strip()
            if choice in ['y', 'yes', '是']:
                print("\n🚀 立即执行测试...")
                self.generate_and_send_report()
        except KeyboardInterrupt:
            print("\n❌ 已取消")
            return
        
        print("\n🔄 进入定时运行模式...")
        print("💡 按 Ctrl+C 停止运行")
        print("=" * 50)
        
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
                
        except KeyboardInterrupt:
            print(f"\n\n⏹️ 用户停止运行")
            print("👋 感谢使用PYTHIA每小时报告发送器！")


class TelegramReportSender:
    """Telegram报告发送器"""
    
    def __init__(self):
        self.bot_token = config.TELEGRAM_BOT_TOKEN
        self.base_url = f"https://api.telegram.org/bot{self.bot_token}"
    
    def _try_send_message(self, chat_id: str, message: str, parse_mode: Optional[str]) -> bool:
        """尝试发送单条消息"""
        try:
            url = f"{self.base_url}/sendMessage"
            
            data = {
                "chat_id": chat_id,
                "text": message,
                "disable_web_page_preview": True
            }
            
            # 只有在指定了parse_mode时才添加
            if parse_mode:
                data["parse_mode"] = parse_mode
            
            response = requests.post(url, json=data, timeout=30)
            
            if response.status_code == 200:
                return True
            else:
                print(f"   ❌ 发送失败 ({parse_mode or 'plain'}): {response.text}")
                return False
                
        except Exception as e:
            print(f"   ❌ 发送异常 ({parse_mode or 'plain'}): {e}")
            return False
        
    def send_message_sync(self, chat_id: str, text: str, parse_mode: str = None) -> bool:
        """同步发送消息到指定聊天 - 直接发送原始文本，不做任何格式处理"""
        try:
            # 直接使用原始文本，不做任何清理或格式化
            raw_text = text
            
            # 分割长消息（Telegram限制4096字符）
            max_length = 4000  # 留一些余量
            if len(raw_text) <= max_length:
                messages = [raw_text]
            else:
                # 简单按字符数分割，保持原始格式
                messages = []
                start = 0
                while start < len(raw_text):
                    end = start + max_length
                    # 尝试在换行符处分割，避免截断单词
                    if end < len(raw_text):
                        last_newline = raw_text.rfind('\n', start, end)
                        if last_newline > start:
                            end = last_newline + 1
                    
                    messages.append(raw_text[start:end])
                    start = end
            
            # 直接发送纯文本，不使用任何格式化
            success_count = 0
            for i, message in enumerate(messages):
                success = self._try_send_message(chat_id, message, None)  # 不使用任何parse_mode
                
                if success:
                    success_count += 1
                    if len(messages) > 1:
                        print(f"   ✅ 消息 {i+1}/{len(messages)} 发送成功")
                        time.sleep(1)  # 避免速率限制
                else:
                    print(f"   ❌ 消息 {i+1}/{len(messages)} 发送失败")
            
            return success_count == len(messages)
            
        except Exception as e:
            print(f"❌ 发送消息失败: {e}")
            return False
    
    async def send_message(self, chat_id: str, text: str, parse_mode: str = "Markdown") -> bool:
        """异步发送消息（兼容性方法）"""
        return self.send_message_sync(chat_id, text, parse_mode)
    
    async def send_to_subscribers(self, report_file_path: str) -> bool:
        """发送报告到所有订阅者（群组和用户）"""
        try:
            # 检查发送配置
            send_config = getattr(config, 'REPORT_SEND_CONFIG', {})
            
            if not send_config.get('auto_send_enabled', True):
                print("📤 自动发送已禁用")
                return True
            
            # 获取要发送的聊天列表
            chat_ids = []
            
            # 添加配置的群组
            if send_config.get('send_to_groups', True):
                group_chats = getattr(config, 'TELEGRAM_REPORT_CHATS', [])
                chat_ids.extend(group_chats)
            
            if not chat_ids:
                print("❌ 没有配置发送目标")
                return False
            
            # 发送报告
            return await self.send_report_text(report_file_path, chat_ids)
            
        except Exception as e:
            print(f"❌ 发送到订阅者失败: {e}")
            return False

    async def send_report_text(self, report_file_path: str, chat_ids: List[str]) -> bool:
        """发送报告文本到多个聊天"""
        try:
            # 读取报告文件
            if not os.path.exists(report_file_path):
                print(f"❌ 报告文件不存在: {report_file_path}")
                return False
            
            with open(report_file_path, 'r', encoding='utf-8') as f:
                report_content = f.read()
            
            if not report_content.strip():
                print("❌ 报告内容为空")
                return False
            
            # 发送完整的详细报告内容
            success_count = 0
            for chat_id in chat_ids:
                print(f"📤 正在发送到: {chat_id}")
                success = self.send_message_sync(chat_id, report_content)
                if success:
                    success_count += 1
                    print(f"   ✅ 发送成功")
                else:
                    print(f"   ❌ 发送失败")
                
                # 避免速率限制
                if len(chat_ids) > 1:
                    time.sleep(2)
            
            return success_count > 0
            
        except Exception as e:
            print(f"❌ 发送报告失败: {e}")
            return False
    
    def test_connection_sync(self) -> bool:
        """测试Telegram Bot连接（同步版本）"""
        try:
            url = f"{self.base_url}/getMe"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("ok"):
                    bot_info = data.get("result", {})
                    print(f"✅ Bot连接成功: @{bot_info.get('username', 'unknown')}")
                    return True
            
            print(f"❌ Bot连接失败: {response.text}")
            return False
                    
        except Exception as e:
            print(f"❌ 测试连接异常: {e}")
            return False
    
    async def test_connection(self) -> bool:
        """测试Telegram Bot连接（异步兼容性方法）"""
        return self.test_connection_sync()


def main():
    """主函数 - 默认启动自动推送模式"""
    analyzer = PythiaIntegratedAnalyzer()
    
    print("🐭 PYTHIA代币全面分析与报告发送器 - 集成版本")
    print("=" * 60)
    
    # 从配置文件读取自动运行设置
    auto_run_config = ANALYSIS_CONFIG.get("auto_run_settings", {})
    
    if auto_run_config.get("enabled", True):
        # 默认启动自动推送模式
        print("🚀 自动启动定时推送模式")
        print(f"⏰ 推送间隔: {auto_run_config.get('interval_minutes', 60)}分钟")
        print(f"📱 启动时发送: {'是' if auto_run_config.get('run_on_startup', True) else '否'}")
        print()
        
        # 检查schedule库
        try:
            import schedule
            analyzer.start_auto_scheduler()
        except ImportError:
            print("❌ 未安装schedule库")
            print("💡 请安装: pip install schedule")
            print("🔄 切换到持续运行模式...")
            analyzer.start_continuous_mode()
    else:
        # 如果配置中禁用了自动运行，显示菜单
        print("⚠️ 自动运行已在配置中禁用，显示手动选择菜单:")
        print("1. 快速分析")
        print("2. 生成完整报告")
        print("3. 生成报告并存储")
        print("4. 启动定时生成")
        print("5. 启动持续运行模式")
        print()
        
        try:
            choice = input("请输入选择 (1-5): ").strip()
            
            if choice == "1":
                analyzer.quick_analysis()
                
            elif choice == "2":
                print("\n🚀 生成完整报告...")
                filepath = analyzer.auto_generate_report()
                if filepath:
                    print(f"✅ 报告已生成: {filepath}")
                
            elif choice == "3":
                print("\n🚀 生成报告并存储...")
                analyzer.generate_and_send_report()
                
            elif choice == "4":
                try:
                    import schedule
                    analyzer.start_auto_scheduler()
                except ImportError:
                    print("❌ 未安装schedule库")
                    print("💡 请安装: pip install schedule")
                    analyzer.start_continuous_mode()
                    
            elif choice == "5":
                analyzer.start_continuous_mode()
                
            # 移除Telegram测试选项
                    
            else:
                print("❌ 无效选择")

        except KeyboardInterrupt:
            print("\n👋 已取消")

def html_monitor_main():
    """HTML文件监控主函数 - 持续监控模式"""
    analyzer = PythiaIntegratedAnalyzer()

    print("🐭 PYTHIA HTML转图片监控器 - 持续监控模式")
    print("=" * 50)
    print("🔄 程序将持续运行，监控新的HTML文件...")
    print("📸 自动转换为图片并发送到Telegram群组")
    print("💡 按 Ctrl+C 退出程序")
    print("=" * 50)

    processed_files = set()  # 记录已处理的文件

    try:
        while True:
            try:
                # 查找最新HTML文件
                latest_file = analyzer.html_converter.find_latest_html()

                if latest_file and str(latest_file) not in processed_files:
                    # 检查冷却时间
                    if analyzer.html_converter.check_cooldown():
                        print(f"⏰ 跳过处理 {latest_file.name} - 仍在冷却期")
                    else:
                        print(f"\n🆕 发现新文件: {latest_file.name}")
                        print("-" * 30)

                        try:
                            # 转换HTML为图片
                            result = analyzer.html_converter.convert_html_to_image(str(latest_file))
                            if result:
                                print(f"\n🎉 转换完成! 图片: {result}")
                                processed_files.add(str(latest_file))

                                # 发送到Telegram群组
                                if analyzer.telegram_bot_token and analyzer.telegram_chat_id:
                                    success = analyzer.html_converter.send_to_telegram(
                                        result, latest_file.name,
                                        analyzer.telegram_bot_token, analyzer.telegram_chat_id
                                    )
                                    if success:
                                        print("✅ 图片已成功发送到Telegram群组")
                                    else:
                                        print("❌ Telegram发送失败")
                                else:
                                    print("⚠️ Telegram配置未设置，跳过发送")

                                # 记录处理时间并开始冷却
                                analyzer.html_converter.last_process_time = datetime.now()
                                print(f"\n❄️ 开始50分钟冷却期...")
                                print(f"🕐 下次可处理时间: {(analyzer.html_converter.last_process_time + timedelta(minutes=50)).strftime('%H:%M:%S')}")

                            else:
                                print(f"\n❌ 转换失败")
                                processed_files.add(str(latest_file))
                        except Exception as e:
                            print(f"\n❌ 转换错误: {e}")
                            # 即使失败也标记为已处理，避免重复尝试
                            processed_files.add(str(latest_file))

                # 等待10秒后再次检查
                print(f"\n⏰ 等待新文件... (已处理 {len(processed_files)} 个文件)")
                time.sleep(10)

            except KeyboardInterrupt:
                print("\n\n👋 用户中断，程序退出")
                break
            except Exception as e:
                print(f"\n⚠️ 监控异常: {e}")
                print("🔄 5秒后继续监控...")
                time.sleep(5)

    except KeyboardInterrupt:
        print("\n\n👋 程序已退出")

if __name__ == "__main__":
    import sys

    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == "--monitor":
        # 启动HTML监控模式
        html_monitor_main()
    else:
        # 启动正常的分析模式
        main()