#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML模板系统使用示例
展示如何在实际项目中使用模板管理器
"""

from html_templates import HTMLTemplateManager
from datetime import datetime
import os


def example_generate_custom_report():
    """示例：生成自定义报告"""
    print("📊 示例：生成自定义PYTHIA分析报告")
    print("=" * 50)
    
    # 初始化模板管理器
    template_manager = HTMLTemplateManager()
    
    # 模拟真实的市场数据
    market_data = {
        'token_address': 'BQ5jRdxkppHviiqkZSzM7t4CsJaXVjNBuRwVgvn8pump',
        'positive_factors': '''
            <li>🚀 强劲的买入压力: 买卖比例2.3:1</li>
            <li>📈 高交易活跃度: 日交易量$15M</li>
            <li>🔄 多DEX支持: 分布在5个DEX</li>
            <li>💎 持续增长的流动性池</li>
            <li>🌟 社区活跃度持续上升</li>
        ''',
        'avg_price_change': 8.75,
        'buy_sell_ratio': 2.3,
        'sentiment': '🚀 极度乐观',
        'volume_to_mcap': 18.5,
        'total_market_cap': 85000000,   # 85M
        'total_volume': 15000000,       # 15M
        'total_liquidity': 5200000,     # 5.2M
        'pairs_count': 23,
        'pairs_table': '''
            <tr>
                <td>PYTHIA/SOL<br><small style="color: #7D8590;">Raydium</small></td>
                <td>$0.000156</td>
                <td style="color: #28A745;">+8.75%</td>
                <td>$6.2M</td>
            </tr>
            <tr>
                <td>PYTHIA/USDC<br><small style="color: #7D8590;">Orca</small></td>
                <td>$0.000157</td>
                <td style="color: #28A745;">+7.23%</td>
                <td>$4.1M</td>
            </tr>
            <tr>
                <td>PYTHIA/USDT<br><small style="color: #7D8590;">Jupiter</small></td>
                <td>$0.000155</td>
                <td style="color: #28A745;">+9.12%</td>
                <td>$2.8M</td>
            </tr>
            <tr>
                <td>PYTHIA/RAY<br><small style="color: #7D8590;">Raydium</small></td>
                <td>$0.000154</td>
                <td style="color: #28A745;">+6.45%</td>
                <td>$1.9M</td>
            </tr>
        ''',
        'liquidity_table': '''
            <tr>
                <td>PYTHIA/SOL<br><small style="color: #7D8590;">Raydium</small></td>
                <td>$2.1M</td>
                <td>40.4%</td>
            </tr>
            <tr>
                <td>PYTHIA/USDC<br><small style="color: #7D8590;">Orca</small></td>
                <td>$1.6M</td>
                <td>30.8%</td>
            </tr>
            <tr>
                <td>PYTHIA/USDT<br><small style="color: #7D8590;">Jupiter</small></td>
                <td>$0.9M</td>
                <td>17.3%</td>
            </tr>
            <tr>
                <td>PYTHIA/RAY<br><small style="color: #7D8590;">Raydium</small></td>
                <td>$0.6M</td>
                <td>11.5%</td>
            </tr>
        '''
    }
    
    # 生成专业报告
    try:
        report_html = template_manager.generate_professional_report(market_data)
        
        # 保存报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"pythia_custom_report_{timestamp}.html"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(report_html)
        
        print(f"✅ 自定义报告生成成功: {filename}")
        print(f"📁 文件大小: {os.path.getsize(filename)} 字节")
        print(f"🌐 可以在浏览器中打开查看效果")
        
        return filename
        
    except Exception as e:
        print(f"❌ 报告生成失败: {e}")
        return None


def example_generate_error_reports():
    """示例：生成不同类型的错误报告"""
    print("\n🔌 示例：生成不同类型的错误报告")
    print("=" * 50)
    
    template_manager = HTMLTemplateManager()
    
    error_types = [
        ("network", "网络连接错误"),
        ("data", "数据获取错误"),
        ("general", "一般系统错误")
    ]
    
    for error_type, description in error_types:
        try:
            error_html = template_manager.generate_error_report(error_type)
            filename = f"error_report_{error_type}.html"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(error_html)
            
            print(f"✅ {description}报告: {filename}")
            
        except Exception as e:
            print(f"❌ {description}报告生成失败: {e}")


def example_customize_templates():
    """示例：如何自定义模板"""
    print("\n🎨 示例：自定义模板指南")
    print("=" * 50)
    
    template_manager = HTMLTemplateManager()
    templates_dir = template_manager.templates_dir
    
    print("📁 模板文件位置:")
    print(f"   {templates_dir.absolute()}")
    print()
    
    print("📄 可自定义的模板文件:")
    template_files = [
        ("main_report.html", "主报告模板 - 包含完整的报告结构"),
        ("error_page.html", "错误页面模板 - 用于显示各种错误信息"),
        ("styles.css", "CSS样式文件 - 控制页面外观和布局"),
        ("chart_script.js", "JavaScript文件 - 处理图表和交互功能")
    ]
    
    for filename, description in template_files:
        file_path = templates_dir / filename
        if file_path.exists():
            size = file_path.stat().st_size
            print(f"   ✅ {filename:<20} - {description} ({size} 字节)")
        else:
            print(f"   ❌ {filename:<20} - 文件不存在")
    
    print("\n🔧 自定义步骤:")
    print("   1. 编辑 templates/ 目录中的文件")
    print("   2. 使用 {{变量名}} 格式添加动态内容")
    print("   3. 修改CSS变量来改变颜色和样式")
    print("   4. 重新运行程序，更改会自动生效")
    
    print("\n💡 常用自定义示例:")
    print("   - 修改颜色主题: 编辑 styles.css 中的 :root 变量")
    print("   - 添加新的数据字段: 在模板中添加 {{新字段名}}")
    print("   - 更改布局: 修改 main_report.html 的HTML结构")
    print("   - 自定义图表: 编辑 chart_script.js 的TradingView配置")


def main():
    """主函数 - 运行所有示例"""
    print("🎯 HTML模板系统使用示例")
    print("=" * 60)
    
    # 示例1: 生成自定义报告
    custom_report = example_generate_custom_report()
    
    # 示例2: 生成错误报告
    example_generate_error_reports()
    
    # 示例3: 自定义模板指南
    example_customize_templates()
    
    print("\n" + "=" * 60)
    print("🎉 所有示例运行完成!")
    
    if custom_report:
        print(f"\n🌟 主要成果:")
        print(f"   - 生成了自定义报告: {custom_report}")
        print(f"   - 创建了错误报告模板")
        print(f"   - 提供了自定义指南")
        
        print(f"\n📖 下一步:")
        print(f"   1. 在浏览器中打开 {custom_report} 查看效果")
        print(f"   2. 根据需要修改 templates/ 目录中的模板文件")
        print(f"   3. 在主程序中使用 HTMLTemplateManager 替代硬编码HTML")


if __name__ == "__main__":
    main()
