#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 HeadlessHTMLConverter 类
"""

from pythia_html_to_image_integrated import HeadlessHTMLConverter

def test_converter():
    print("🧪 测试 HeadlessHTMLConverter")
    
    try:
        # 创建转换器实例
        converter = HeadlessHTMLConverter()
        print(f"✅ HeadlessHTMLConverter 实例创建成功")
        print(f"📁 输出目录: {converter.output_dir}")
        
        # 测试驱动器设置
        print("🔧 测试浏览器驱动器设置...")
        driver = converter.setup_driver()
        print("✅ Chrome驱动器设置成功")
        
        # 关闭驱动器
        driver.quit()
        print("✅ 驱动器已关闭")
        
        print("🎉 所有测试通过!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_converter()
