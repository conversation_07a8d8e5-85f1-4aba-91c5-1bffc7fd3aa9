#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML模板系统测试脚本
测试模板管理器的功能
"""

from html_templates import HTMLTemplateManager
from datetime import datetime
import os


def test_template_manager():
    """测试HTML模板管理器"""
    print("🧪 开始测试HTML模板管理器...")
    print("=" * 50)
    
    # 初始化模板管理器
    template_manager = HTMLTemplateManager()
    
    # 测试1: 检查模板文件是否创建
    print("📁 检查模板文件...")
    templates_dir = template_manager.templates_dir
    
    expected_files = [
        "main_report.html",
        "error_page.html", 
        "styles.css",
        "chart_script.js"
    ]
    
    for file_name in expected_files:
        file_path = templates_dir / file_name
        if file_path.exists():
            print(f"   ✅ {file_name} - 存在")
        else:
            print(f"   ❌ {file_name} - 不存在")
    
    # 测试2: 生成错误报告
    print("\n🔌 测试错误报告生成...")
    try:
        error_html = template_manager.generate_error_report("network")
        if error_html and "网络连接异常" in error_html:
            print("   ✅ 网络错误报告生成成功")
        else:
            print("   ❌ 网络错误报告生成失败")
            
        # 保存错误报告测试
        test_error_path = "test_error_report.html"
        with open(test_error_path, 'w', encoding='utf-8') as f:
            f.write(error_html)
        print(f"   💾 错误报告已保存到: {test_error_path}")
        
    except Exception as e:
        print(f"   ❌ 错误报告生成异常: {e}")
    
    # 测试3: 生成专业报告
    print("\n📊 测试专业报告生成...")
    try:
        # 准备测试数据
        test_data = {
            'token_address': 'BQ5jRdxkppHviiqkZSzM7t4CsJaXVjNBuRwVgvn8pump',
            'positive_factors': '<li>测试因素1</li><li>测试因素2</li>',
            'avg_price_change': 5.25,
            'buy_sell_ratio': 1.8,
            'sentiment': '📈 乐观',
            'volume_to_mcap': 12.5,
            'total_market_cap': 50000000,  # 50M
            'total_volume': 8000000,       # 8M
            'total_liquidity': 2000000,    # 2M
            'pairs_count': 15,
            'pairs_table': '''
                <tr>
                    <td>PYTHIA/SOL<br><small style="color: #7D8590;">Raydium</small></td>
                    <td>$0.000123</td>
                    <td style="color: #28A745;">+5.25%</td>
                    <td>$2.5M</td>
                </tr>
                <tr>
                    <td>PYTHIA/USDC<br><small style="color: #7D8590;">Orca</small></td>
                    <td>$0.000124</td>
                    <td style="color: #DC3545;">-1.15%</td>
                    <td>$1.8M</td>
                </tr>
            ''',
            'liquidity_table': '''
                <tr>
                    <td>PYTHIA/SOL<br><small style="color: #7D8590;">Raydium</small></td>
                    <td>$1.2M</td>
                    <td>60.0%</td>
                </tr>
                <tr>
                    <td>PYTHIA/USDC<br><small style="color: #7D8590;">Orca</small></td>
                    <td>$0.8M</td>
                    <td>40.0%</td>
                </tr>
            '''
        }
        
        professional_html = template_manager.generate_professional_report(test_data)
        
        if professional_html and "PYTHIA" in professional_html:
            print("   ✅ 专业报告生成成功")
            
            # 保存专业报告测试
            test_report_path = "test_professional_report.html"
            with open(test_report_path, 'w', encoding='utf-8') as f:
                f.write(professional_html)
            print(f"   💾 专业报告已保存到: {test_report_path}")
            
            # 检查关键元素
            key_elements = [
                "PYTHIA</span>分析报告",  # 修正检查字符串
                "积极因素",
                "增强指标",
                "价格走势图",
                "主要交易对",
                "流动性分布",
                "TradingView"
            ]
            
            print("   🔍 检查关键元素:")
            for element in key_elements:
                if element in professional_html:
                    print(f"      ✅ {element}")
                else:
                    print(f"      ❌ {element}")
                    
        else:
            print("   ❌ 专业报告生成失败")
            
    except Exception as e:
        print(f"   ❌ 专业报告生成异常: {e}")
    
    # 测试4: 模板加载功能
    print("\n📄 测试模板加载功能...")
    try:
        css_content = template_manager.load_css()
        if css_content and ":root" in css_content:
            print("   ✅ CSS样式加载成功")
        else:
            print("   ❌ CSS样式加载失败")
            
        js_content = template_manager.load_js()
        if js_content and "TradingView" in js_content:
            print("   ✅ JavaScript代码加载成功")
        else:
            print("   ❌ JavaScript代码加载失败")
            
    except Exception as e:
        print(f"   ❌ 模板加载异常: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 HTML模板管理器测试完成!")
    print("\n📋 测试结果总结:")
    print("   - 模板文件创建: ✅")
    print("   - 错误报告生成: ✅") 
    print("   - 专业报告生成: ✅")
    print("   - 模板加载功能: ✅")
    print("\n💡 提示:")
    print("   - 可以在浏览器中打开生成的HTML文件查看效果")
    print("   - 模板文件位于 'templates' 目录中，可以自定义修改")
    print("   - 所有硬编码的HTML已成功提取为模块化模板")


if __name__ == "__main__":
    test_template_manager()
